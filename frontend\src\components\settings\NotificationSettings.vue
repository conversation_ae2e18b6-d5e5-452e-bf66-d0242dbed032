<template>
  <div class="notification-settings">
    <div class="settings-section-header">
      <h3 class="section-title">
        <el-icon><Bell /></el-icon>
        通知设置
      </h3>
      <p class="section-description">管理您接收通知的方式</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      label-width="140px"
      class="notification-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 邮件通知 -->
      <el-form-item label="邮件通知">
        <div class="notification-item">
          <div class="notification-content">
            <el-switch
              v-model="formData.emailNotifications"
              size="large"
              :active-icon="Message"
              :inactive-icon="MessageBox"
            />
            <div class="notification-info">
              <div class="notification-title">邮件通知</div>
              <div class="notification-desc">
                接收重要活动和更新的邮件通知
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 评论通知 -->
      <el-form-item label="评论通知">
        <div class="notification-item">
          <div class="notification-content">
            <el-switch
              v-model="formData.commentNotifications"
              size="large"
              :active-icon="ChatDotRound"
              :inactive-icon="ChatLineRound"
            />
            <div class="notification-info">
              <div class="notification-title">评论通知</div>
              <div class="notification-desc">
                当有人评论您的文章或回复您的评论时通知您
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 系统通知 -->
      <el-form-item label="系统通知">
        <div class="notification-item">
          <div class="notification-content">
            <el-switch
              v-model="formData.systemNotifications"
              size="large"
              :active-icon="Bell"
              :inactive-icon="MuteNotification"
            />
            <div class="notification-info">
              <div class="notification-title">系统通知</div>
              <div class="notification-desc">
                接收系统维护、功能更新等重要系统消息
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 通知摘要 -->
      <el-form-item label="通知摘要">
        <el-card class="notification-summary" shadow="never">
          <div class="summary-content">
            <div class="summary-item">
              <el-icon class="summary-icon enabled"><Bell /></el-icon>
              <span class="summary-text">
                已启用 {{ enabledCount }} / {{ totalCount }} 项通知
              </span>
            </div>
            <div class="summary-actions">
              <el-button 
                size="small" 
                type="primary" 
                plain
                @click="enableAll"
              >
                全部启用
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                plain
                @click="disableAll"
              >
                全部关闭
              </el-button>
            </div>
          </div>
        </el-card>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            保存通知设置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { 
  Bell, 
  Message, 
  MessageBox, 
  ChatDotRound, 
  ChatLineRound, 
  MuteNotification, 
  Check 
} from '@element-plus/icons-vue'

import type { UserSettings, SettingsUpdateParams } from '@/services/settings'

// ==================== Props & Emits ====================

interface Props {
  settings: UserSettings | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  update: [data: SettingsUpdateParams]
}>()

// ==================== 响应式数据 ====================

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  emailNotifications: true,
  commentNotifications: true,
  systemNotifications: true
})

// ==================== 计算属性 ====================

// 已启用的通知数量
const enabledCount = computed(() => {
  return [
    formData.emailNotifications,
    formData.commentNotifications,
    formData.systemNotifications
  ].filter(Boolean).length
})

// 总通知数量
const totalCount = computed(() => 3)

// ==================== 监听器 ====================

// 监听设置数据变化，更新表单
watch(
  () => props.settings,
  (newSettings) => {
    if (newSettings) {
      formData.emailNotifications = newSettings.emailNotifications ?? true
      formData.commentNotifications = newSettings.commentNotifications ?? true
      formData.systemNotifications = newSettings.systemNotifications ?? true
    }
  },
  { immediate: true }
)

// ==================== 方法 ====================

/**
 * 启用所有通知
 */
const enableAll = () => {
  formData.emailNotifications = true
  formData.commentNotifications = true
  formData.systemNotifications = true
  ElMessage.success('已启用所有通知')
}

/**
 * 关闭所有通知
 */
const disableAll = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要关闭所有通知吗？您将不会收到任何通知消息。',
      '关闭所有通知',
      {
        confirmButtonText: '确定关闭',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    formData.emailNotifications = false
    formData.commentNotifications = false
    formData.systemNotifications = false
    ElMessage.success('已关闭所有通知')
  } catch (error) {
    // 用户取消操作
  }
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  // 准备更新数据
  const updateData: SettingsUpdateParams = {
    emailNotifications: formData.emailNotifications,
    commentNotifications: formData.commentNotifications,
    systemNotifications: formData.systemNotifications
  }

  emit('update', updateData)
}

/**
 * 重置表单
 */
const handleReset = () => {
  if (props.settings) {
    formData.emailNotifications = props.settings.emailNotifications ?? true
    formData.commentNotifications = props.settings.commentNotifications ?? true
    formData.systemNotifications = props.settings.systemNotifications ?? true
  }
  
  ElMessage.success('表单已重置')
}
</script>

<style scoped>
.notification-settings {
  max-width: 600px;
}

.settings-section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.notification-form {
  margin-top: 24px;
}

.notification-item {
  width: 100%;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
  transition: all 0.3s ease;
}

.notification-content:hover {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.notification-info {
  flex: 1;
}

.notification-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.notification-summary {
  border: 1px solid var(--el-border-color-light);
  background: var(--el-bg-color-page);
}

.summary-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-icon {
  font-size: 18px;
}

.summary-icon.enabled {
  color: var(--el-color-success);
}

.summary-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.summary-actions {
  display: flex;
  gap: 8px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .summary-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .summary-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
