import { Notification, NotificationPreference, User } from '../models'
import { Op } from 'sequelize'

/**
 * 通知服务类
 * 处理通知的创建、发送和管理逻辑
 */
export class NotificationService {
  /**
   * 创建文章评论通知
   */
  static async createArticleCommentNotification(data: {
    articleId: number
    articleTitle: string
    authorId: number
    commenterId: number
    commenterName: string
    commentContent: string
  }): Promise<void> {
    const { articleId, articleTitle, authorId, commenterId, commenterName, commentContent } = data

    // 不给自己发通知
    if (authorId === commenterId) return

    // 检查用户是否启用了互动通知
    const isEnabled = await NotificationPreference.isNotificationEnabled(
      authorId,
      'interaction',
      'in_app'
    )

    if (!isEnabled) return

    await Notification.createInteractionNotification({
      recipientId: authorId,
      senderId: commenterId,
      title: `${commenterName} 评论了你的文章`,
      content: `在文章《${articleTitle}》中评论：${commentContent.substring(0, 100)}${commentContent.length > 100 ? '...' : ''}`,
      relatedType: 'article',
      relatedId: articleId,
      actionUrl: `/articles/${articleId}#comments`
    })
  }

  /**
   * 创建说说评论通知
   */
  static async createPostCommentNotification(data: {
    postId: number
    postContent: string
    authorId: number
    commenterId: number
    commenterName: string
    commentContent: string
  }): Promise<void> {
    const { postId, postContent, authorId, commenterId, commenterName, commentContent } = data

    // 不给自己发通知
    if (authorId === commenterId) return

    // 检查用户是否启用了互动通知
    const isEnabled = await NotificationPreference.isNotificationEnabled(
      authorId,
      'interaction',
      'in_app'
    )

    if (!isEnabled) return

    await Notification.createInteractionNotification({
      recipientId: authorId,
      senderId: commenterId,
      title: `${commenterName} 评论了你的说说`,
      content: `在说说"${postContent.substring(0, 50)}${postContent.length > 50 ? '...' : ''}"中评论：${commentContent.substring(0, 100)}${commentContent.length > 100 ? '...' : ''}`,
      relatedType: 'post',
      relatedId: postId,
      actionUrl: `/posts/${postId}#comments`
    })
  }

  /**
   * 创建评论回复通知
   */
  static async createCommentReplyNotification(data: {
    originalCommentId: number
    originalCommenterId: number
    replierId: number
    replierName: string
    replyContent: string
    relatedType: 'article' | 'post'
    relatedId: number
  }): Promise<void> {
    const { originalCommenterId, replierId, replierName, replyContent, relatedType, relatedId } = data

    // 不给自己发通知
    if (originalCommenterId === replierId) return

    // 检查用户是否启用了互动通知
    const isEnabled = await NotificationPreference.isNotificationEnabled(
      originalCommenterId,
      'interaction',
      'in_app'
    )

    if (!isEnabled) return

    const typeText = relatedType === 'article' ? '文章' : '说说'
    const actionUrl = relatedType === 'article' 
      ? `/articles/${relatedId}#comments`
      : `/posts/${relatedId}#comments`

    await Notification.createInteractionNotification({
      recipientId: originalCommenterId,
      senderId: replierId,
      title: `${replierName} 回复了你的评论`,
      content: `在${typeText}中回复：${replyContent.substring(0, 100)}${replyContent.length > 100 ? '...' : ''}`,
      relatedType: 'comment',
      relatedId: relatedId,
      actionUrl
    })
  }

  /**
   * 创建说说点赞通知
   */
  static async createPostLikeNotification(data: {
    postId: number
    postContent: string
    authorId: number
    likerId: number
    likerName: string
  }): Promise<void> {
    const { postId, postContent, authorId, likerId, likerName } = data

    // 不给自己发通知
    if (authorId === likerId) return

    // 检查用户是否启用了互动通知
    const isEnabled = await NotificationPreference.isNotificationEnabled(
      authorId,
      'interaction',
      'in_app'
    )

    if (!isEnabled) return

    await Notification.createInteractionNotification({
      recipientId: authorId,
      senderId: likerId,
      title: `${likerName} 赞了你的说说`,
      content: `"${postContent.substring(0, 100)}${postContent.length > 100 ? '...' : ''}"`,
      relatedType: 'post',
      relatedId: postId,
      actionUrl: `/posts/${postId}`
    })
  }

  /**
   * 创建新文章发布通知（给关注者）
   */
  static async createNewArticleNotification(data: {
    articleId: number
    articleTitle: string
    authorId: number
    authorName: string
    followerIds: number[]
  }): Promise<void> {
    const { articleId, articleTitle, authorId, authorName, followerIds } = data

    if (followerIds.length === 0) return

    // 获取启用了内容通知的关注者
    const enabledFollowers = await NotificationPreference.getEnabledUsers('content', 'in_app')
    const targetFollowers = followerIds.filter(id => enabledFollowers.includes(id))

    if (targetFollowers.length === 0) return

    // 批量创建通知
    const notifications = targetFollowers.map(followerId => ({
      type: 'content' as const,
      title: `${authorName} 发布了新文章`,
      content: `《${articleTitle}》`,
      priority: 'medium' as const,
      recipientId: followerId,
      senderId: authorId,
      relatedType: 'article' as const,
      relatedId: articleId,
      actionUrl: `/articles/${articleId}`
    }))

    await Notification.createBulkNotifications(notifications)
  }

  /**
   * 创建系统维护通知
   */
  static async createSystemMaintenanceNotification(data: {
    title: string
    content: string
    maintenanceTime: Date
    userIds?: number[]
  }): Promise<void> {
    const { title, content, maintenanceTime, userIds } = data

    let targetUsers: number[]

    if (userIds && userIds.length > 0) {
      // 发送给指定用户
      targetUsers = userIds
    } else {
      // 发送给所有启用了系统通知的用户
      targetUsers = await NotificationPreference.getEnabledUsers('system', 'in_app')
    }

    if (targetUsers.length === 0) return

    // 批量创建通知
    const notifications = targetUsers.map(userId => ({
      type: 'system' as const,
      title,
      content: `${content}\n维护时间：${maintenanceTime.toLocaleString()}`,
      priority: 'high' as const,
      recipientId: userId,
      relatedType: 'system' as const,
      actionUrl: '/system/maintenance'
    }))

    await Notification.createBulkNotifications(notifications)
  }

  /**
   * 创建安全提醒通知
   */
  static async createSecurityNotification(data: {
    userId: number
    title: string
    content: string
    actionUrl?: string
  }): Promise<void> {
    const { userId, title, content, actionUrl } = data

    // 安全通知强制发送，不检查用户偏好
    await Notification.createSystemNotification({
      recipientId: userId,
      title,
      content,
      priority: 'high',
      actionUrl: actionUrl || '/settings/security'
    })
  }

  /**
   * 清理过期通知（定时任务）
   */
  static async cleanupExpiredNotifications(): Promise<number> {
    return await Notification.cleanupExpiredNotifications()
  }

  /**
   * 获取用户的通知统计信息
   */
  static async getUserNotificationStats(userId: number): Promise<{
    total: number
    unread: number
    byType: Record<string, number>
    byPriority: Record<string, number>
  }> {
    const [total, unread, byType, byPriority] = await Promise.all([
      // 总通知数
      Notification.count({ where: { recipientId: userId } }),
      
      // 未读通知数
      Notification.count({ where: { recipientId: userId, isRead: false } }),
      
      // 按类型统计
      Notification.findAll({
        where: { recipientId: userId },
        attributes: [
          'type',
          [Notification.sequelize!.fn('COUNT', '*'), 'count']
        ],
        group: ['type'],
        raw: true
      }),
      
      // 按优先级统计
      Notification.findAll({
        where: { recipientId: userId },
        attributes: [
          'priority',
          [Notification.sequelize!.fn('COUNT', '*'), 'count']
        ],
        group: ['priority'],
        raw: true
      })
    ])

    return {
      total,
      unread,
      byType: byType.reduce((acc: any, item: any) => {
        acc[item.type] = parseInt(item.count)
        return acc
      }, {}),
      byPriority: byPriority.reduce((acc: any, item: any) => {
        acc[item.priority] = parseInt(item.count)
        return acc
      }, {})
    }
  }
}
