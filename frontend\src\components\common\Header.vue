<template>
  <header class="header">
    <div class="container">
      <div class="nav-brand">
        <RouterLink to="/" class="brand-link">
          <h1 class="brand-title">个人博客</h1>
        </RouterLink>
      </div>

      <nav class="nav-menu">
        <RouterLink to="/" class="nav-link">首页</RouterLink>
        <RouterLink to="/article" class="nav-link">文章</RouterLink>
        <RouterLink to="/posts" class="nav-link">说说</RouterLink>
        <RouterLink to="/category" class="nav-link">分类</RouterLink>
        <RouterLink to="/tags" class="nav-link">标签</RouterLink>
        <!-- 通知图标仅在用户已认证时显示 -->
        <NotificationBadge v-if="authStore.isAuthenticated" />
        <!-- 管理页面链接仅在用户已认证时显示 -->
        <RouterLink v-if="authStore.isAuthenticated" to="/admin" class="nav-link">管理</RouterLink>
        <!-- 设置页面链接仅在用户已认证时显示 -->
        <RouterLink v-if="authStore.isAuthenticated" to="/settings" class="nav-link">设置</RouterLink>
        <!-- 登录链接仅在用户未认证时显示 -->
        <RouterLink v-if="!authStore.isAuthenticated" to="/login" class="nav-link">登录</RouterLink>
        <!-- 设计系统链接仅在开发环境显示 -->
        <RouterLink v-if="isDevelopment" to="/design-system" class="nav-link dev-link">设计系统</RouterLink>
        <!-- 登出按钮仅在用户已认证时显示 -->
        <button v-if="authStore.isAuthenticated" @click="handleLogout" class="nav-link logout-btn">
          登出
        </button>

        <!-- 主题切换组件 -->
        <ThemeToggle mode="dropdown" />
      </nav>
    </div>
  </header>
</template>

<script setup lang="ts">
import { RouterLink, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import ThemeToggle from './ThemeToggle.vue'
import NotificationBadge from '@/components/notification/NotificationBadge.vue'

/**
 * 使用认证状态管理仓库
 */
const authStore = useAuthStore()

/**
 * 获取路由实例用于页面跳转
 */
const router = useRouter()

/**
 * 判断当前是否为开发环境
 */
const isDevelopment = import.meta.env.DEV

/**
 * 处理用户登出逻辑
 * 
 * 用户登出后，清除认证状态并跳转到首页
 */
const handleLogout = () => {
  authStore.logout()
  router.push('/')
}
</script>

<style scoped>
/* 头部导航栏样式 */
.header {
  background-color: var(--color-nav-bg);
  border-bottom: 1px solid var(--color-nav-border);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* 容器布局样式 */
.container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 品牌链接样式 */
.brand-link {
  text-decoration: none;
  color: var(--color-text-primary);
  transition: color var(--transition-fast);
}

.brand-link:hover {
  color: var(--color-primary);
}

/* 品牌标题样式 */
.brand-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航菜单样式 */
.nav-menu {
  display: flex;
  gap: var(--spacing-6);
  align-items: center;
}

/* 导航链接通用样式 */
.nav-link {
  text-decoration: none;
  color: var(--color-nav-text);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--color-nav-text-hover);
  background-color: var(--color-surface-hover);
}

/* 激活状态的导航链接样式 */
.nav-link.router-link-active {
  color: var(--color-nav-text-active);
  background-color: var(--color-primary-light);
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

/* 登出按钮样式 */
.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
}

.logout-btn:hover {
  color: var(--color-error);
}

/* 开发环境链接特殊样式 */
.dev-link {
  background-color: var(--color-warning-bg);
  color: var(--color-warning);
  border: 1px solid var(--color-warning-border);
}

.dev-link:hover {
  background-color: var(--color-warning);
  color: var(--color-text-inverse);
}

/* 平板及以下设备响应式样式 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .nav-menu {
    gap: var(--spacing-3);
  }

  .brand-title {
    font-size: var(--font-size-lg);
  }

  .nav-link {
    padding: var(--spacing-2);
    font-size: var(--font-size-xs);
  }
}

/* 手机设备响应式样式 */
@media (max-width: 480px) {
  .nav-menu {
    gap: var(--spacing-2);
  }

  .nav-link {
    padding: var(--spacing-1) var(--spacing-2);
  }
}

/* 高对比度模式样式适配 */
@media (prefers-contrast: high) {
  .header {
    border-bottom-width: 2px;
  }

  .brand-title {
    -webkit-text-fill-color: unset;
    background: unset;
    color: var(--color-text-primary);
  }
}

/* 减少动画模式样式适配 */
@media (prefers-reduced-motion: reduce) {
  .nav-link::after {
    transition: none;
  }
}
</style>