import { Request, Response, NextFunction } from 'express'
import <PERSON><PERSON> from 'joi'
import { createError } from './errorHandler'

/**
 * 设置更新验证中间件
 * 验证设置更新请求的数据格式和内容
 */
export const validateSettingsUpdate = (req: Request, res: Response, next: NextFunction): void => {
  const schema = Joi.object({
    // 个人信息设置
    displayName: Joi.string().min(1).max(100).optional().messages({
      'string.min': '显示名称不能为空',
      'string.max': '显示名称不能超过100个字符'
    }),
    avatar: Joi.string().uri().optional().allow('').messages({
      'string.uri': '头像必须是有效的URL地址'
    }),
    bio: Joi.string().max(500).optional().allow('').messages({
      'string.max': '个人简介不能超过500个字符'
    }),
    website: Joi.string().uri().optional().allow('').messages({
      'string.uri': '网站必须是有效的URL地址'
    }),
    location: Joi.string().min(1).max(100).optional().allow('').messages({
      'string.min': '所在地不能为空',
      'string.max': '所在地不能超过100个字符'
    }),
    
    // 偏好设置
    theme: Joi.string().valid('light', 'dark', 'auto').optional().messages({
      'any.only': '主题必须是 light、dark 或 auto'
    }),
    language: Joi.string().min(2).max(10).optional().messages({
      'string.min': '语言代码至少需要2个字符',
      'string.max': '语言代码不能超过10个字符'
    }),
    timezone: Joi.string().min(1).max(50).optional().messages({
      'string.min': '时区不能为空',
      'string.max': '时区不能超过50个字符'
    }),
    itemsPerPage: Joi.number().integer().min(5).max(100).optional().messages({
      'number.min': '每页显示数量不能少于5',
      'number.max': '每页显示数量不能超过100',
      'number.integer': '每页显示数量必须是整数'
    }),
    
    // 通知设置
    emailNotifications: Joi.boolean().optional().messages({
      'boolean.base': '邮件通知设置必须是布尔值'
    }),
    commentNotifications: Joi.boolean().optional().messages({
      'boolean.base': '评论通知设置必须是布尔值'
    }),
    systemNotifications: Joi.boolean().optional().messages({
      'boolean.base': '系统通知设置必须是布尔值'
    }),
    
    // 隐私设置
    profileVisibility: Joi.string().valid('public', 'private').optional().messages({
      'any.only': '个人资料可见性必须是 public 或 private'
    }),
    defaultPostVisibility: Joi.string().valid('public', 'private').optional().messages({
      'any.only': '默认文章可见性必须是 public 或 private'
    }),
    showEmail: Joi.boolean().optional().messages({
      'boolean.base': '显示邮箱设置必须是布尔值'
    }),
    
    // 安全设置
    twoFactorEnabled: Joi.boolean().optional().messages({
      'boolean.base': '两步验证设置必须是布尔值'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个设置项'
  })

  const { error, value } = schema.validate(req.body, { 
    abortEarly: false,
    stripUnknown: true 
  })

  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ')
    throw createError(400, errorMessage, 'VALIDATION_ERROR')
  }

  // 将验证后的数据替换原始请求体
  req.body = value
  next()
}

/**
 * 设置安全验证中间件
 * 对敏感设置进行额外的安全检查
 */
export const validateSettingsSecurity = (req: Request, res: Response, next: NextFunction): void => {
  const { twoFactorEnabled, profileVisibility, defaultPostVisibility } = req.body

  // 如果要启用两步验证，需要额外验证
  if (twoFactorEnabled === true) {
    // 这里可以添加额外的验证逻辑，比如检查用户是否已设置邮箱等
    // 暂时允许通过，实际项目中可能需要更复杂的验证
  }

  // 验证可见性设置的合理性
  if (profileVisibility === 'private' && defaultPostVisibility === 'public') {
    // 警告：个人资料设为私有但默认文章可见性为公开可能不合理
    // 这里只是记录警告，不阻止操作
    console.warn('User setting profile to private but default post visibility to public')
  }

  next()
}

/**
 * 设置数据清理中间件
 * 清理和标准化设置数据
 */
export const sanitizeSettingsData = (req: Request, res: Response, next: NextFunction): void => {
  const data = req.body

  // 清理字符串字段
  if (data.displayName) {
    data.displayName = data.displayName.trim()
  }

  if (data.bio) {
    data.bio = data.bio.trim()
  }

  if (data.location) {
    data.location = data.location.trim()
  }

  // 标准化URL字段
  if (data.website) {
    data.website = data.website.trim()
    // 如果没有协议前缀，添加https://
    if (data.website && !data.website.match(/^https?:\/\//)) {
      data.website = `https://${data.website}`
    }
  }

  if (data.avatar) {
    data.avatar = data.avatar.trim()
  }

  // 标准化语言代码
  if (data.language) {
    data.language = data.language.toLowerCase()
  }

  next()
}

/**
 * 设置权限验证中间件
 * 验证用户是否有权限修改特定设置
 */
export const validateSettingsPermissions = (req: Request, res: Response, next: NextFunction): void => {
  // 这里可以添加基于用户角色的权限验证
  // 例如：某些高级设置只有管理员可以修改
  
  // 暂时允许所有认证用户修改自己的设置
  next()
}

/**
 * 组合设置验证中间件
 * 将所有设置相关的验证中间件组合在一起
 */
export const settingsValidationMiddleware = [
  validateSettingsUpdate,
  sanitizeSettingsData,
  validateSettingsSecurity,
  validateSettingsPermissions
]
