<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑说说' : '发布说说'"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <!-- 说说内容 -->
      <el-form-item label="内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="分享你的想法..."
          maxlength="1000"
          show-word-limit
          resize="none"
        />
      </el-form-item>

      <!-- 图片上传 -->
      <el-form-item label="图片" prop="images">
        <div class="image-upload-container">
          <el-upload
            v-model:file-list="imageList"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeImageUpload"
            :on-success="handleImageSuccess"
            :on-error="handleImageError"
            :on-remove="handleImageRemove"
            list-type="picture-card"
            :limit="9"
            multiple
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">
                最多上传9张图片，支持 jpg/png/gif 格式，单张不超过5MB
              </div>
            </template>
          </el-upload>
        </div>
      </el-form-item>

      <!-- 位置信息 -->
      <el-form-item label="位置" prop="location">
        <el-input
          v-model="form.location"
          placeholder="添加位置信息（可选）"
          maxlength="200"
          clearable
        >
          <template #prefix>
            <el-icon><Location /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 可见性设置 -->
      <el-form-item label="可见性" prop="visibility">
        <el-radio-group v-model="form.visibility">
          <el-radio value="public">
            <el-icon><View /></el-icon>
            公开
          </el-radio>
          <el-radio value="private">
            <el-icon><Lock /></el-icon>
            私密
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '发布' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadFile, type UploadFiles } from 'element-plus'
import { Plus, Location, View, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { Post, PostCreateRequest, PostUpdateRequest } from '@/services/types/post'

/**
 * 组件属性定义
 */
interface Props {
  /** 是否显示对话框 */
  modelValue: boolean
  /** 编辑的说说数据（编辑模式） */
  post?: Post
}

const props = defineProps<Props>()

/**
 * 组件事件定义
 */
interface Emits {
  /** 更新显示状态 */
  'update:modelValue': [value: boolean]
  /** 提交事件 */
  submit: [data: PostCreateRequest | PostUpdateRequest]
  /** 取消事件 */
  cancel: []
}

const emit = defineEmits<Emits>()

/**
 * 响应式数据
 */
const formRef = ref<FormInstance>()
const submitting = ref(false)
const imageList = ref<UploadFiles>([])

const form = reactive({
  content: '',
  images: [] as string[],
  location: '',
  visibility: 'public' as 'public' | 'private'
})

/**
 * 计算属性
 */
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.post)

const authStore = useAuthStore()
const uploadUrl = computed(() => '/api/upload/image')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

/**
 * 表单验证规则
 */
const rules: FormRules = {
  content: [
    { required: true, message: '请输入说说内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '内容长度在 1 到 1000 个字符', trigger: 'blur' }
  ],
  location: [
    { max: 200, message: '位置信息不能超过200个字符', trigger: 'blur' }
  ]
}

/**
 * 监听器
 */
watch(() => props.post, (newPost) => {
  if (newPost) {
    // 编辑模式，填充表单数据
    form.content = newPost.content
    form.images = newPost.images || []
    form.location = newPost.location || ''
    form.visibility = newPost.visibility
    
    // 设置图片列表
    imageList.value = (newPost.images || []).map((url, index) => ({
      name: `image-${index}`,
      url,
      uid: Date.now() + index
    }))
  }
}, { immediate: true })

watch(visible, (newVisible) => {
  if (newVisible && !props.post) {
    // 新建模式，重置表单
    resetForm()
  }
})

/**
 * 图片上传处理
 */
const beforeImageUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
  const isValidSize = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只能上传 JPG/PNG/GIF 格式的图片!')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleImageSuccess = (response: any, file: UploadFile) => {
  if (response.success && response.data?.url) {
    form.images.push(response.data.url)
  } else {
    ElMessage.error('图片上传失败')
    // 移除失败的文件
    const index = imageList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      imageList.value.splice(index, 1)
    }
  }
}

const handleImageError = (error: any, file: UploadFile) => {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
  // 移除失败的文件
  const index = imageList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    imageList.value.splice(index, 1)
  }
}

const handleImageRemove = (file: UploadFile) => {
  // 从表单数据中移除对应的图片URL
  if (file.url) {
    const index = form.images.indexOf(file.url)
    if (index > -1) {
      form.images.splice(index, 1)
    }
  }
}

/**
 * 表单处理
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      content: form.content.trim(),
      images: form.images,
      location: form.location.trim() || undefined,
      visibility: form.visibility
    }

    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  if (submitting.value) return
  emit('cancel')
  visible.value = false
}

const resetForm = () => {
  form.content = ''
  form.images = []
  form.location = ''
  form.visibility = 'public'
  imageList.value = []
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/**
 * 暴露给父组件的方法
 */
defineExpose({
  resetForm
})
</script>

<style scoped>
.image-upload-container {
  width: 100%;
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 80px;
  height: 80px;
}

:deep(.el-radio) {
  display: flex;
  align-items: center;
  margin-right: 24px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
