import { Request, Response, NextFunction } from 'express'
import { Settings } from '../models/Settings'
import { User } from '../models/User'
import { createError } from '../middleware/errorHandler'
import Joi from 'joi'

/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
  }
}

/**
 * 更新设置请求参数的校验规则
 */
const updateSettingsSchema = Joi.object({
  // 个人信息设置
  displayName: Joi.string().min(1).max(100).optional(),
  avatar: Joi.string().uri().optional().allow(''),
  bio: Joi.string().max(500).optional().allow(''),
  website: Joi.string().uri().optional().allow(''),
  location: Joi.string().min(1).max(100).optional().allow(''),
  
  // 偏好设置
  theme: Joi.string().valid('light', 'dark', 'auto').optional(),
  language: Joi.string().min(2).max(10).optional(),
  timezone: Joi.string().min(1).max(50).optional(),
  itemsPerPage: Joi.number().integer().min(5).max(100).optional(),
  
  // 通知设置
  emailNotifications: Joi.boolean().optional(),
  commentNotifications: Joi.boolean().optional(),
  systemNotifications: Joi.boolean().optional(),
  
  // 隐私设置
  profileVisibility: Joi.string().valid('public', 'private').optional(),
  defaultPostVisibility: Joi.string().valid('public', 'private').optional(),
  showEmail: Joi.boolean().optional(),
  
  // 安全设置
  twoFactorEnabled: Joi.boolean().optional()
})

/**
 * 获取用户设置
 * @param req - AuthenticatedRequest 请求对象，包含用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getSettings = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      throw createError(401, 'Authentication required', 'UNAUTHORIZED')
    }

    // 查找用户设置
    let settings = await Settings.findByUserId(req.user.id)

    // 如果用户没有设置记录，创建默认设置
    if (!settings) {
      const defaultSettings = Settings.getDefaultSettings()
      settings = await Settings.upsertByUserId(req.user.id, defaultSettings)
    }

    res.json({
      success: true,
      message: '获取设置成功',
      data: settings
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 更新用户设置
 * @param req - AuthenticatedRequest 请求对象，包含用户认证信息和设置数据
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const updateSettings = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      throw createError(401, 'Authentication required', 'UNAUTHORIZED')
    }

    // 校验请求体参数
    const { error, value } = updateSettingsSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    // 使用模型的验证方法
    const validation = Settings.validateSettings(value)
    if (!validation.valid) {
      throw createError(400, validation.errors.join(', '), 'VALIDATION_ERROR')
    }

    // 更新设置
    const settings = await Settings.upsertByUserId(req.user.id, value)

    res.json({
      success: true,
      message: '设置更新成功',
      data: settings
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 重置用户设置为默认值
 * @param req - AuthenticatedRequest 请求对象，包含用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const resetSettings = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      throw createError(401, 'Authentication required', 'UNAUTHORIZED')
    }

    // 获取默认设置并更新
    const defaultSettings = Settings.getDefaultSettings()
    const settings = await Settings.upsertByUserId(req.user.id, defaultSettings)

    res.json({
      success: true,
      message: '设置已重置为默认值',
      data: settings
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取用户的完整信息（包含设置）
 * @param req - AuthenticatedRequest 请求对象，包含用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getUserProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      throw createError(401, 'Authentication required', 'UNAUTHORIZED')
    }

    // 查找用户及其设置
    const user = await User.findByPk(req.user.id, {
      include: [
        {
          model: Settings,
          as: 'settings'
        }
      ]
    })

    if (!user) {
      throw createError(404, 'User not found', 'USER_NOT_FOUND')
    }

    // 如果用户没有设置，创建默认设置
    if (!user.settings) {
      const defaultSettings = Settings.getDefaultSettings()
      const settings = await Settings.upsertByUserId(req.user.id, defaultSettings)
      
      // 重新查询用户信息
      const updatedUser = await User.findByPk(req.user.id, {
        include: [
          {
            model: Settings,
            as: 'settings'
          }
        ]
      })

      res.json({
        success: true,
        message: '获取用户信息成功',
        data: updatedUser
      })
      return
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: user
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取设置的默认值
 * @param req - Express 请求对象
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getDefaultSettings = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const defaultSettings = Settings.getDefaultSettings()

    res.json({
      success: true,
      message: '获取默认设置成功',
      data: defaultSettings
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 验证设置数据
 * @param req - Express 请求对象，包含要验证的设置数据
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const validateSettingsData = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 校验请求体参数
    const { error, value } = updateSettingsSchema.validate(req.body)
    if (error) {
      res.json({
        success: false,
        message: 'Validation failed',
        errors: error.details.map(detail => detail.message)
      })
      return
    }

    // 使用模型的验证方法
    const validation = Settings.validateSettings(value)

    res.json({
      success: validation.valid,
      message: validation.valid ? 'Validation passed' : 'Validation failed',
      errors: validation.errors
    })
  } catch (error) {
    next(error)
  }
}
