<template>
  <div 
    class="media-card"
    :class="{ 
      'media-card--selected': isSelected,
      'media-card--loading': loading 
    }"
    @click="handleClick"
  >
    <!-- 选择框 -->
    <div v-if="selectable" class="media-card__checkbox">
      <el-checkbox 
        :model-value="isSelected"
        @change="handleSelect"
        @click.stop
      />
    </div>

    <!-- 媒体预览 -->
    <div class="media-card__preview">
      <!-- 图片预览 -->
      <div v-if="media.category === 'image'" class="media-card__image">
        <el-image
          :src="media.thumbnailUrl || media.url"
          :alt="media.originalName"
          fit="cover"
          lazy
          :preview-src-list="[media.url]"
          :initial-index="0"
          :hide-on-click-modal="true"
        >
          <template #error>
            <div class="media-card__error">
              <el-icon><Picture /></el-icon>
              <span>加载失败</span>
            </div>
          </template>
        </el-image>
      </div>

      <!-- 视频预览 -->
      <div v-else-if="media.category === 'video'" class="media-card__video">
        <video 
          :src="media.url"
          :poster="media.thumbnailUrl"
          preload="metadata"
          @click.stop
        />
        <div class="media-card__play-icon">
          <el-icon><VideoPlay /></el-icon>
        </div>
      </div>

      <!-- 音频预览 -->
      <div v-else-if="media.category === 'audio'" class="media-card__audio">
        <div class="media-card__audio-icon">
          <el-icon><Headphone /></el-icon>
        </div>
        <audio :src="media.url" preload="metadata" @click.stop />
      </div>

      <!-- 文档预览 -->
      <div v-else class="media-card__document">
        <div class="media-card__document-icon">
          <el-icon><Document /></el-icon>
        </div>
      </div>

      <!-- 媒体信息覆盖层 -->
      <div class="media-card__overlay">
        <div class="media-card__info">
          <span class="media-card__size">{{ formattedSize }}</span>
          <span v-if="media.width && media.height" class="media-card__dimensions">
            {{ media.width }} × {{ media.height }}
          </span>
        </div>
        
        <!-- 操作按钮 -->
        <div class="media-card__actions">
          <el-button
            type="primary"
            size="small"
            circle
            @click.stop="handlePreview"
          >
            <el-icon><View /></el-icon>
          </el-button>
          
          <el-button
            type="info"
            size="small"
            circle
            @click.stop="handleEdit"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
          
          <el-button
            type="danger"
            size="small"
            circle
            @click.stop="handleDelete"
            :loading="deleting"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 媒体详情 -->
    <div class="media-card__details">
      <div class="media-card__title" :title="media.originalName">
        {{ media.originalName }}
      </div>
      
      <div class="media-card__meta">
        <span class="media-card__category">
          {{ categoryLabels[media.category] }}
        </span>
        <span class="media-card__date">
          {{ formatDate(media.createdAt) }}
        </span>
      </div>

      <!-- 标签 -->
      <div v-if="media.tags && media.tags.length > 0" class="media-card__tags">
        <el-tag
          v-for="tag in media.tags.slice(0, 3)"
          :key="tag"
          size="small"
          type="info"
        >
          {{ tag }}
        </el-tag>
        <el-tag v-if="media.tags.length > 3" size="small" type="info">
          +{{ media.tags.length - 3 }}
        </el-tag>
      </div>

      <!-- 上传者信息 -->
      <div class="media-card__uploader">
        <el-icon><User /></el-icon>
        <span>{{ media.uploader.username }}</span>
        <el-icon v-if="!media.isPublic" class="media-card__private-icon">
          <Lock />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Picture,
  VideoPlay,
  Headphone,
  Document,
  View,
  Edit,
  Delete,
  User,
  Lock
} from '@element-plus/icons-vue'
import { MediaUtils } from '@/services/media'
import type { MediaWithUploader } from '@/types/media'

// ==================== 组件属性 ====================
interface Props {
  media: MediaWithUploader
  selectable?: boolean
  isSelected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selectable: false,
  isSelected: false
})

// ==================== 组件事件 ====================
interface Emits {
  select: [id: number, selected: boolean]
  preview: [media: MediaWithUploader]
  edit: [media: MediaWithUploader]
  delete: [media: MediaWithUploader]
  click: [media: MediaWithUploader]
}

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const loading = ref(false)
const deleting = ref(false)

// ==================== 计算属性 ====================
const formattedSize = computed(() => MediaUtils.formatFileSize(props.media.size))

const categoryLabels = {
  image: '图片',
  video: '视频',
  audio: '音频',
  document: '文档'
}

// ==================== 方法 ====================
/**
 * 格式化日期
 */
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

/**
 * 处理卡片点击
 */
const handleClick = () => {
  emit('click', props.media)
}

/**
 * 处理选择
 */
const handleSelect = (selected: boolean) => {
  emit('select', props.media.id, selected)
}

/**
 * 处理预览
 */
const handlePreview = () => {
  emit('preview', props.media)
}

/**
 * 处理编辑
 */
const handleEdit = () => {
  emit('edit', props.media)
}

/**
 * 处理删除
 */
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除媒体文件 "${props.media.originalName}" 吗？`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    deleting.value = true
    emit('delete', props.media)
  } catch {
    // 用户取消删除
  } finally {
    deleting.value = false
  }
}
</script>

<style scoped lang="scss">
.media-card {
  position: relative;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .media-card__overlay {
      opacity: 1;
    }
  }

  &--selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  &--loading {
    opacity: 0.6;
    pointer-events: none;
  }

  &__checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
  }

  &__preview {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
  }

  &__image,
  &__video,
  &__audio,
  &__document {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__image {
    .el-image {
      width: 100%;
      height: 100%;
    }
  }

  &__video {
    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  &__audio,
  &__document {
    background: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
    font-size: 48px;
  }

  &__error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--el-text-color-secondary);
    font-size: 14px;

    .el-icon {
      font-size: 32px;
    }
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &__info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    color: white;
    font-size: 12px;
  }

  &__actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }

  &__details {
    padding: 12px;
  }

  &__title {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  &__tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-bottom: 8px;
  }

  &__uploader {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  &__private-icon {
    color: var(--el-color-warning);
  }
}
</style>
