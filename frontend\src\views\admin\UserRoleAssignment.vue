<template>
  <div class="user-role-assignment">
    <div class="page-header">
      <h1>用户角色分配</h1>
      <p>管理用户的角色分配和权限</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="search-section">
        <el-input
          v-model="searchForm.search"
          placeholder="搜索用户名、邮箱或角色名"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.roleId"
          placeholder="筛选角色"
          style="width: 200px; margin-left: 12px"
          clearable
        >
          <el-option
            v-for="role in rbacStore.activeRoles"
            :key="role.id"
            :label="role.name"
            :value="role.id"
          />
        </el-select>
        
        <el-button type="primary" @click="handleSearch" style="margin-left: 12px">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="button-section">
        <el-button type="primary" @click="handleAssignRole">
          <el-icon><Plus /></el-icon>
          分配角色
        </el-button>
      </div>
    </div>

    <!-- 用户角色表格 -->
    <div class="table-container">
      <el-table
        v-loading="rbacStore.loading.userRoles"
        :data="rbacStore.userRoles"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="user.username" label="用户名" min-width="120">
          <template #default="{ row }">
            <div class="user-info">
              <span class="username">{{ row.user?.username }}</span>
              <span v-if="row.user?.nickname" class="nickname">
                ({{ row.user.nickname }})
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="user.email" label="邮箱" min-width="200" />
        
        <el-table-column prop="role.name" label="角色" min-width="150">
          <template #default="{ row }">
            <div class="role-info">
              <el-tag
                :type="row.role?.isSystem ? 'info' : 'primary'"
                size="small"
              >
                {{ row.role?.name }}
              </el-tag>
              <span v-if="row.role?.isSystem" class="system-badge">
                系统角色
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="role.description" label="角色描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="assignedByUser" label="分配者" min-width="120">
          <template #default="{ row }">
            {{ row.assignedByUser?.nickname || row.assignedByUser?.username || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="assignedAt" label="分配时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.assignedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewUserRoles(row)"
            >
              查看权限
            </el-button>
            
            <el-popconfirm
              title="确定要移除这个角色分配吗？"
              @confirm="handleRemoveRole(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="small"
                  :disabled="row.role?.isSystem"
                >
                  移除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="rbacStore.userRolesPagination.page"
          v-model:page-size="rbacStore.userRolesPagination.limit"
          :total="rbacStore.userRolesPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 角色分配对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="分配角色"
      width="600px"
      @close="handleAssignDialogClose"
    >
      <el-form
        ref="assignFormRef"
        :model="assignForm"
        :rules="assignFormRules"
        label-width="100px"
      >
        <el-form-item label="选择用户" prop="userId">
          <el-select
            v-model="assignForm.userId"
            placeholder="请选择用户"
            style="width: 100%"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="userSearchLoading"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="`${user.username} (${user.email})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择角色" prop="roleId">
          <el-select
            v-model="assignForm.roleId"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in rbacStore.activeRoles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            >
              <div class="role-option">
                <span>{{ role.name }}</span>
                <span v-if="role.description" class="role-desc">
                  - {{ role.description }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitAssign" :loading="assigning">
          分配
        </el-button>
      </template>
    </el-dialog>

    <!-- 用户权限查看对话框 -->
    <UserPermissionDialog
      v-model="permissionDialogVisible"
      :user-role="selectedUserRole"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { useRbacStore } from '@/stores/rbac'
import { formatDate } from '@/utils/date'
import type { UserRole, AssignRoleData } from '@/services/rbac'
import UserPermissionDialog from '@/components/admin/UserPermissionDialog.vue'

// Store
const rbacStore = useRbacStore()

// 响应式数据
const searchForm = reactive({
  search: '',
  roleId: undefined as number | undefined
})

const assignDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const selectedUserRole = ref<UserRole | null>(null)
const assigning = ref(false)
const userSearchLoading = ref(false)

// 用户搜索相关
const userOptions = ref<any[]>([])

// 分配表单
const assignFormRef = ref()
const assignForm = reactive<AssignRoleData>({
  userId: 0,
  roleId: 0
})

const assignFormRules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 方法
const loadUserRoles = async () => {
  await rbacStore.fetchUserRoles({
    page: rbacStore.userRolesPagination.page,
    limit: rbacStore.userRolesPagination.limit,
    ...searchForm
  })
}

const loadRoles = async () => {
  await rbacStore.fetchRoles({ limit: 1000 })
}

const handleSearch = () => {
  rbacStore.userRolesPagination.page = 1
  loadUserRoles()
}

const handleReset = () => {
  searchForm.search = ''
  searchForm.roleId = undefined
  rbacStore.userRolesPagination.page = 1
  loadUserRoles()
}

const handleAssignRole = () => {
  assignForm.userId = 0
  assignForm.roleId = 0
  userOptions.value = []
  assignDialogVisible.value = true
}

const handleViewUserRoles = (userRole: UserRole) => {
  selectedUserRole.value = userRole
  permissionDialogVisible.value = true
}

const handleRemoveRole = async (userRole: UserRole) => {
  const success = await rbacStore.removeUserRole(userRole.id)
  if (success) {
    loadUserRoles()
  }
}

const handleSubmitAssign = async () => {
  try {
    await assignFormRef.value.validate()
    assigning.value = true
    
    const success = await rbacStore.assignRoleToUser(assignForm)
    if (success) {
      assignDialogVisible.value = false
      loadUserRoles()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    assigning.value = false
  }
}

const handleAssignDialogClose = () => {
  assignFormRef.value?.resetFields()
}

const searchUsers = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  
  try {
    userSearchLoading.value = true
    // 这里应该调用用户搜索API
    // const users = await UserService.searchUsers(query)
    // userOptions.value = users
    
    // 临时模拟数据
    userOptions.value = [
      { id: 1, username: 'admin', email: '<EMAIL>' },
      { id: 2, username: 'user1', email: '<EMAIL>' },
      { id: 3, username: 'user2', email: '<EMAIL>' }
    ].filter(user => 
      user.username.includes(query) || user.email.includes(query)
    )
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    userSearchLoading.value = false
  }
}

const handlePageChange = (page: number) => {
  rbacStore.userRolesPagination.page = page
  loadUserRoles()
}

const handleSizeChange = (size: number) => {
  rbacStore.userRolesPagination.limit = size
  rbacStore.userRolesPagination.page = 1
  loadUserRoles()
}

// 生命周期
onMounted(() => {
  loadUserRoles()
  loadRoles()
})
</script>

<style scoped>
.user-role-assignment {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
}

.search-section {
  display: flex;
  align-items: center;
}

.button-section {
  display: flex;
  gap: 12px;
}

.table-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 500;
}

.nickname {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.system-badge {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.role-option {
  display: flex;
  align-items: center;
}

.role-desc {
  color: var(--el-text-color-regular);
  font-size: 12px;
}
</style>
