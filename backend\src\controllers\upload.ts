import { Request, Response } from 'express'
import multer from 'multer'
import path from 'path'
import fs from 'fs'
import { v4 as uuidv4 } from 'uuid'
import { Media } from '../models/Media'

/**
 * 扩展Request接口，添加用户信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 上传文件配置
 */
const uploadConfig = {
  // 文件大小限制（5MB）
  maxFileSize: 5 * 1024 * 1024,
  // 允许的文件类型
  allowedMimeTypes: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp'
  ],
  // 上传目录
  uploadDir: path.join(process.cwd(), 'uploads', 'images'),
  // 最大文件数量
  maxFiles: 9
}

/**
 * 确保上传目录存在
 */
const ensureUploadDir = (): void => {
  if (!fs.existsSync(uploadConfig.uploadDir)) {
    fs.mkdirSync(uploadConfig.uploadDir, { recursive: true })
  }
}

/**
 * 配置multer存储
 */
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    ensureUploadDir()
    cb(null, uploadConfig.uploadDir)
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名：UUID + 原始扩展名
    const ext = path.extname(file.originalname)
    const filename = `${uuidv4()}${ext}`
    cb(null, filename)
  }
})

/**
 * 文件过滤器
 */
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 检查文件类型
  if (uploadConfig.allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`))
  }
}

/**
 * 配置multer
 */
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: uploadConfig.maxFileSize,
    files: uploadConfig.maxFiles
  }
})

/**
 * 图片上传控制器类
 */
export class UploadController {
  /**
   * 单个图片上传
   */
  public static uploadSingle = upload.single('image')

  /**
   * 多个图片上传
   */
  public static uploadMultiple = upload.array('images', uploadConfig.maxFiles)

  /**
   * 处理单个图片上传
   */
  public static async handleSingleUpload(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: '没有上传文件'
        })
        return
      }

      const baseUrl = `${req.protocol}://${req.get('host')}`
      const imageUrl = `${baseUrl}/uploads/images/${req.file.filename}`

      // 创建媒体记录
      let mediaRecord = null
      if (req.user) {
        try {
          mediaRecord = await Media.create({
            filename: req.file.filename,
            originalName: req.file.originalname,
            mimeType: req.file.mimetype,
            size: req.file.size,
            url: imageUrl,
            uploaderId: req.user.id,
            category: Media.getCategoryFromMimeType(req.file.mimetype),
            isPublic: true
          })
        } catch (dbError) {
          console.warn('创建媒体记录失败:', dbError)
          // 继续返回上传成功，即使数据库记录创建失败
        }
      }

      res.json({
        success: true,
        message: '图片上传成功',
        data: {
          id: mediaRecord?.id,
          filename: req.file.filename,
          originalName: req.file.originalname,
          size: req.file.size,
          url: imageUrl,
          mimeType: req.file.mimetype,
          category: Media.getCategoryFromMimeType(req.file.mimetype)
        }
      })
    } catch (error) {
      console.error('单个图片上传失败:', error)
      res.status(500).json({
        success: false,
        message: '图片上传失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 处理多个图片上传
   */
  public static async handleMultipleUpload(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        res.status(400).json({
          success: false,
          message: '没有上传文件'
        })
        return
      }

      const baseUrl = `${req.protocol}://${req.get('host')}`
      const uploadedFiles = []
      const mediaRecords = []

      // 处理每个上传的文件
      for (const file of req.files) {
        const imageUrl = `${baseUrl}/uploads/images/${file.filename}`
        const fileData: any = {
          filename: file.filename,
          originalName: file.originalname,
          size: file.size,
          url: imageUrl,
          mimeType: file.mimetype,
          category: Media.getCategoryFromMimeType(file.mimetype)
        }

        // 创建媒体记录
        if (req.user) {
          try {
            const mediaRecord = await Media.create({
              filename: file.filename,
              originalName: file.originalname,
              mimeType: file.mimetype,
              size: file.size,
              url: imageUrl,
              uploaderId: req.user.id,
              category: Media.getCategoryFromMimeType(file.mimetype),
              isPublic: true
            })
            mediaRecords.push(mediaRecord)
            // 添加媒体记录ID到文件数据
            fileData.id = mediaRecord.id
          } catch (dbError) {
            console.warn(`创建媒体记录失败 (${file.filename}):`, dbError)
            // 继续处理其他文件
          }
        }

        uploadedFiles.push(fileData)
      }

      res.json({
        success: true,
        message: `成功上传 ${uploadedFiles.length} 张图片`,
        data: {
          files: uploadedFiles,
          count: uploadedFiles.length,
          mediaRecords: mediaRecords.length
        }
      })
    } catch (error) {
      console.error('多个图片上传失败:', error)
      res.status(500).json({
        success: false,
        message: '图片上传失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 删除图片文件
   */
  public static async deleteImage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { filename } = req.params

      if (!filename) {
        res.status(400).json({
          success: false,
          message: '缺少文件名参数'
        })
        return
      }

      // 验证文件名格式（防止路径遍历攻击）
      const safeFilename = path.basename(filename)
      if (safeFilename !== filename) {
        res.status(400).json({
          success: false,
          message: '无效的文件名'
        })
        return
      }

      const filePath = path.join(uploadConfig.uploadDir, safeFilename)

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        res.status(404).json({
          success: false,
          message: '文件不存在'
        })
        return
      }

      // 删除文件
      fs.unlinkSync(filePath)

      res.json({
        success: true,
        message: '图片删除成功'
      })
    } catch (error) {
      console.error('删除图片失败:', error)
      res.status(500).json({
        success: false,
        message: '删除图片失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取图片信息
   */
  public static async getImageInfo(req: Request, res: Response): Promise<void> {
    try {
      const { filename } = req.params

      if (!filename) {
        res.status(400).json({
          success: false,
          message: '缺少文件名参数'
        })
        return
      }

      // 验证文件名格式
      const safeFilename = path.basename(filename)
      if (safeFilename !== filename) {
        res.status(400).json({
          success: false,
          message: '无效的文件名'
        })
        return
      }

      const filePath = path.join(uploadConfig.uploadDir, safeFilename)

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        res.status(404).json({
          success: false,
          message: '文件不存在'
        })
        return
      }

      // 获取文件信息
      const stats = fs.statSync(filePath)
      const baseUrl = `${req.protocol}://${req.get('host')}`

      res.json({
        success: true,
        data: {
          filename: safeFilename,
          size: stats.size,
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime,
          url: `${baseUrl}/uploads/images/${safeFilename}`
        }
      })
    } catch (error) {
      console.error('获取图片信息失败:', error)
      res.status(500).json({
        success: false,
        message: '获取图片信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取上传配置信息
   */
  public static getUploadConfig(_req: Request, res: Response): void {
    res.json({
      success: true,
      data: {
        maxFileSize: uploadConfig.maxFileSize,
        maxFiles: uploadConfig.maxFiles,
        allowedTypes: uploadConfig.allowedMimeTypes,
        maxFileSizeMB: Math.round(uploadConfig.maxFileSize / (1024 * 1024))
      }
    })
  }
}
