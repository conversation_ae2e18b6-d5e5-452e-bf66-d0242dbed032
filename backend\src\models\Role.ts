import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 角色模型的属性接口，定义了角色对象的基本字段结构
 */
export interface RoleAttributes {
  id: number
  name: string
  description?: string
  isActive: boolean
  isSystem: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 角色创建时的属性接口，继承自 RoleAttributes，但允许部分字段为空
 */
export interface RoleCreationAttributes extends Optional<RoleAttributes, 'id' | 'isActive' | 'isSystem' | 'createdAt' | 'updatedAt'> {}

/**
 * 角色模型类，用于与数据库中的 roles 表进行交互
 * 实现了 RoleAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class Role extends Model<RoleAttributes, RoleCreationAttributes> implements RoleAttributes {
  public id!: number
  public name!: string
  public description?: string
  public isActive!: boolean
  public isSystem!: boolean
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly users?: any[]
  public readonly permissions?: any[]

  public static associations: {
    users: Association<Role, any>
    permissions: Association<Role, any>
  }

  /**
   * 根据角色名称查找角色
   * @param name - 角色名称
   * @returns 返回角色实例或null
   */
  public static async findByName(name: string): Promise<Role | null> {
    return this.findOne({ where: { name } })
  }

  /**
   * 获取所有活跃的角色
   * @returns 返回活跃角色列表
   */
  public static async getActiveRoles(): Promise<Role[]> {
    return this.findAll({ 
      where: { isActive: true },
      order: [['name', 'ASC']]
    })
  }

  /**
   * 获取非系统角色（可以被删除的角色）
   * @returns 返回非系统角色列表
   */
  public static async getNonSystemRoles(): Promise<Role[]> {
    return this.findAll({ 
      where: { isSystem: false },
      order: [['name', 'ASC']]
    })
  }

  /**
   * 检查角色是否可以被删除
   * @returns 返回是否可以删除
   */
  public canBeDeleted(): boolean {
    return !this.isSystem
  }

  /**
   * 检查角色是否可以被修改
   * @returns 返回是否可以修改
   */
  public canBeModified(): boolean {
    return !this.isSystem
  }

  /**
   * 获取角色的权限数量
   * @returns 返回权限数量
   */
  public async getPermissionCount(): Promise<number> {
    const RolePermission = sequelize.models.RolePermission
    return RolePermission.count({ where: { roleId: this.id } })
  }

  /**
   * 获取角色的用户数量
   * @returns 返回用户数量
   */
  public async getUserCount(): Promise<number> {
    const UserRole = sequelize.models.UserRole
    return UserRole.count({ where: { roleId: this.id } })
  }

  /**
   * 为角色分配权限
   * @param permissionIds - 权限ID数组
   * @returns 返回操作结果
   */
  public async assignPermissions(permissionIds: number[]): Promise<void> {
    const RolePermission = sequelize.models.RolePermission
    
    // 删除现有权限
    await RolePermission.destroy({ where: { roleId: this.id } })
    
    // 添加新权限
    if (permissionIds.length > 0) {
      const rolePermissions = permissionIds.map(permissionId => ({
        roleId: this.id,
        permissionId
      }))
      await RolePermission.bulkCreate(rolePermissions)
    }
  }

  /**
   * 获取角色的所有权限
   * @returns 返回权限列表
   */
  public async getPermissions(): Promise<any[]> {
    const Permission = sequelize.models.Permission
    const RolePermission = sequelize.models.RolePermission
    
    const rolePermissions = await RolePermission.findAll({
      where: { roleId: this.id },
      include: [{
        model: Permission,
        as: 'permission'
      }]
    })
    
    return rolePermissions.map((rp: any) => rp.permission)
  }

  /**
   * 检查角色是否拥有特定权限
   * @param permissionName - 权限名称
   * @returns 返回是否拥有权限
   */
  public async hasPermission(permissionName: string): Promise<boolean> {
    const Permission = sequelize.models.Permission
    const RolePermission = sequelize.models.RolePermission
    
    const count = await RolePermission.count({
      where: { roleId: this.id },
      include: [{
        model: Permission,
        as: 'permission',
        where: { name: permissionName }
      }]
    })
    
    return count > 0
  }

  /**
   * 序列化角色信息，用于API响应
   * @returns 返回序列化后的角色信息
   */
  public toJSON(): Omit<RoleAttributes, never> {
    const values = { ...this.get() }
    return values
  }
}

/**
 * 初始化 Role 模型，配置其字段、验证规则和索引
 */
Role.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 50],
        notEmpty: true
      },
      comment: '角色名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '角色描述'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
      comment: '是否激活'
    },
    isSystem: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_system',
      comment: '是否为系统角色（不可删除）'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'Role',
    tableName: 'roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['is_system']
      }
    ]
  }
)
