import { Request, Response, NextFunction } from 'express'
import { Role } from '../models/Role'
import { Permission } from '../models/Permission'
import { RolePermission } from '../models/RolePermission'
import { UserRole } from '../models/UserRole'
import { createError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/permission'

/**
 * 角色管理控制器
 * 提供角色的CRUD操作和权限分配功能
 */

/**
 * 获取角色列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getRoles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { page = 1, limit = 10, search, isActive } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const offset = (pageNum - 1) * limitNum

    // 构建查询条件
    const whereClause: any = {}
    
    if (search) {
      whereClause.name = { [require('sequelize').Op.like]: `%${search}%` }
    }
    
    if (isActive !== undefined) {
      whereClause.isActive = isActive === 'true'
    }

    // 查询角色列表
    const { rows: roles, count: total } = await Role.findAndCountAll({
      where: whereClause,
      limit: limitNum,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    })

    // 为每个角色添加统计信息
    const rolesWithStats = await Promise.all(
      roles.map(async (role) => {
        const userCount = await role.getUserCount()
        const permissionCount = await role.getPermissionCount()
        
        return {
          ...role.toJSON(),
          userCount,
          permissionCount
        }
      })
    )

    res.json({
      success: true,
      data: {
        roles: rolesWithStats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据ID获取角色详情
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getRoleById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    const role = await Role.findByPk(id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: ['assignedAt', 'assignedBy'] }
        }
      ]
    })

    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 获取角色统计信息
    const userCount = await role.getUserCount()
    const permissionCount = await role.getPermissionCount()

    res.json({
      success: true,
      data: {
        ...role.toJSON(),
        userCount,
        permissionCount
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 创建新角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const createRole = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { name, description, isActive = true, permissionIds = [] } = req.body

    // 检查角色名称是否已存在
    const existingRole = await Role.findByName(name)
    if (existingRole) {
      throw createError(400, '角色名称已存在', 'ROLE_NAME_EXISTS')
    }

    // 创建角色
    const role = await Role.create({
      name,
      description,
      isActive,
      isSystem: false
    })

    // 分配权限
    if (permissionIds.length > 0) {
      await role.assignPermissions(permissionIds)
    }

    // 重新获取角色信息（包含权限）
    const createdRole = await Role.findByPk(role.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    })

    res.status(201).json({
      success: true,
      data: createdRole,
      message: '角色创建成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 更新角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const updateRole = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const { name, description, isActive, permissionIds } = req.body

    const role = await Role.findByPk(id)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 检查是否为系统角色
    if (role.isSystem && (name !== role.name || isActive === false)) {
      throw createError(400, '系统角色不能修改名称或禁用', 'SYSTEM_ROLE_IMMUTABLE')
    }

    // 如果修改名称，检查是否与其他角色重复
    if (name && name !== role.name) {
      const existingRole = await Role.findByName(name)
      if (existingRole) {
        throw createError(400, '角色名称已存在', 'ROLE_NAME_EXISTS')
      }
    }

    // 更新角色基本信息
    await role.update({
      name: name || role.name,
      description: description !== undefined ? description : role.description,
      isActive: isActive !== undefined ? isActive : role.isActive
    })

    // 更新权限分配
    if (permissionIds !== undefined) {
      await role.assignPermissions(permissionIds)
    }

    // 重新获取角色信息（包含权限）
    const updatedRole = await Role.findByPk(role.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    })

    res.json({
      success: true,
      data: updatedRole,
      message: '角色更新成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 删除角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const deleteRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    const role = await Role.findByPk(id)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 检查是否为系统角色
    if (role.isSystem) {
      throw createError(400, '系统角色不能删除', 'SYSTEM_ROLE_UNDELETABLE')
    }

    // 检查是否有用户使用此角色
    const userCount = await role.getUserCount()
    if (userCount > 0) {
      throw createError(400, `角色正在被 ${userCount} 个用户使用，无法删除`, 'ROLE_IN_USE')
    }

    // 删除角色（级联删除相关的权限分配）
    await role.destroy()

    res.json({
      success: true,
      message: '角色删除成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取角色的权限列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getRolePermissions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    const role = await Role.findByPk(id)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    const permissions = await role.getPermissions()

    res.json({
      success: true,
      data: permissions
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 为角色分配权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const assignRolePermissions = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const { permissionIds } = req.body
    const assignedBy = req.user?.id

    const role = await Role.findByPk(id)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 验证权限ID是否有效
    if (permissionIds && permissionIds.length > 0) {
      const validPermissions = await Permission.findAll({
        where: { id: permissionIds, isActive: true }
      })

      if (validPermissions.length !== permissionIds.length) {
        throw createError(400, '包含无效的权限ID', 'INVALID_PERMISSION_IDS')
      }
    }

    // 分配权限
    await RolePermission.assignPermissions(role.id, permissionIds || [], assignedBy)

    // 获取更新后的权限列表
    const permissions = await role.getPermissions()

    res.json({
      success: true,
      data: permissions,
      message: '权限分配成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取角色的用户列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getRoleUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const { page = 1, limit = 10 } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const offset = (pageNum - 1) * limitNum

    const role = await Role.findByPk(id)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 获取拥有此角色的用户
    const userRoles = await UserRole.findByRoleId(parseInt(id), {
      limit: limitNum,
      offset,
      include: ['user', 'assigner']
    })

    const total = await UserRole.getRoleUserCount(parseInt(id))

    res.json({
      success: true,
      data: {
        users: userRoles,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error) {
    next(error)
  }
}
