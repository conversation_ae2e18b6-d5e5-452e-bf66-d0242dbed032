<template>
  <div class="post-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="container">
        <el-skeleton :rows="8" animated />
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="container">
        <el-result
          icon="error"
          title="加载失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="loadPost">
              重试
            </el-button>
            <el-button @click="goBack">
              返回
            </el-button>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 正常内容 -->
    <div v-else-if="post" class="post-content">
      <div class="container">
        <!-- 返回按钮 -->
        <div class="back-button">
          <el-button :icon="ArrowLeft" @click="goBack">
            返回说说列表
          </el-button>
        </div>

        <!-- 说说主体 -->
        <el-card class="post-card">
          <!-- 作者信息 -->
          <div class="post-header">
            <div class="author-info">
              <el-avatar :size="48" :src="getAvatarUrl(post.author?.username)">
                {{ post.author?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="author-details">
                <div class="author-name">{{ post.author?.username }}</div>
                <div class="post-time">{{ formatDate(post.createdAt) }}</div>
              </div>
            </div>
            
            <!-- 可见性标识 -->
            <div v-if="post.visibility === 'private'" class="visibility-badge">
              <el-tag type="warning" size="small">
                <el-icon><Lock /></el-icon>
                私密
              </el-tag>
            </div>
          </div>

          <!-- 说说内容 -->
          <div class="post-body">
            <div class="post-text">{{ post.content }}</div>
            
            <!-- 图片展示 -->
            <div v-if="post.images && post.images.length > 0" class="post-images">
              <el-image
                v-for="(image, index) in post.images"
                :key="index"
                :src="image"
                :preview-src-list="post.images"
                :initial-index="index"
                fit="cover"
                :class="getImageClass(post.images.length, index)"
                lazy
              />
            </div>

            <!-- 位置信息 -->
            <div v-if="post.location" class="post-location">
              <el-icon><Location /></el-icon>
              <span>{{ post.location }}</span>
            </div>
          </div>

          <!-- 操作栏 -->
          <div class="post-actions">
            <div class="action-buttons">
              <!-- 点赞 -->
              <el-button
                :type="post.isLiked ? 'primary' : 'default'"
                :icon="post.isLiked ? 'HeartFilled' : 'Heart'"
                @click="handleLike"
              >
                {{ post.likeCount || 0 }} 点赞
              </el-button>
              
              <!-- 评论 -->
              <el-button
                type="default"
                :icon="ChatLineRound"
                @click="scrollToComments"
              >
                {{ post.commentCount || 0 }} 评论
              </el-button>
              
              <!-- 分享 */
              <el-button
                type="default"
                :icon="Share"
                @click="handleShare"
              >
                分享
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 评论区域 -->
        <div id="comments" class="comments-section">
          <el-card>
            <template #header>
              <div class="comments-header">
                <h3>评论 ({{ post.commentCount || 0 }})</h3>
              </div>
            </template>
            
            <!-- 评论表单 -->
            <div v-if="isAuthenticated" class="comment-form">
              <el-input
                v-model="commentText"
                type="textarea"
                :rows="3"
                placeholder="写下你的评论..."
                maxlength="500"
                show-word-limit
                resize="none"
              />
              <div class="comment-actions">
                <el-button
                  type="primary"
                  :loading="submittingComment"
                  :disabled="!commentText.trim()"
                  @click="handleSubmitComment"
                >
                  发表评论
                </el-button>
              </div>
            </div>
            
            <!-- 未登录提示 -->
            <div v-else class="login-prompt">
              <el-alert
                title="请登录后发表评论"
                type="info"
                show-icon
                :closable="false"
              >
                <template #default>
                  <el-button type="primary" size="small" @click="goToLogin">
                    立即登录
                  </el-button>
                </template>
              </el-alert>
            </div>

            <!-- 评论列表 -->
            <div class="comments-list">
              <CommentList
                :comments="comments"
                :loading="commentsLoading"
                :pagination="commentsPagination"
                @reply="handleReply"
                @like="handleCommentLike"
                @delete="handleCommentDelete"
                @load-more="loadMoreComments"
              />
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Lock,
  Location,
  Heart,
  HeartFilled,
  ChatLineRound,
  Share
} from '@element-plus/icons-vue'
import { usePostStore } from '@/stores/post'
import { useAuthStore } from '@/stores/auth'
import { commentService } from '@/services/comment'
import CommentList from '@/components/blog/comment/CommentList.vue'
import type { Post } from '@/services/types/post'
import type { Comment, CommentCreateRequest } from '@/services/types/comment'

/**
 * 路由和状态管理
 */
const route = useRoute()
const router = useRouter()
const postStore = usePostStore()
const authStore = useAuthStore()

/**
 * 响应式数据
 */
const loading = ref(false)
const error = ref<string | null>(null)
const post = ref<Post | null>(null)
const comments = ref<Comment[]>([])
const commentsLoading = ref(false)
const commentsPagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 20,
  hasNextPage: false,
  hasPrevPage: false
})
const commentText = ref('')
const submittingComment = ref(false)

/**
 * 计算属性
 */
const postId = computed(() => Number(route.params.id))
const isAuthenticated = computed(() => authStore.isAuthenticated)

/**
 * 生命周期
 */
onMounted(async () => {
  await loadPost()
  await loadComments()
  
  // 设置页面标题
  if (post.value) {
    document.title = `${post.value.author?.username}的说说 - 个人博客`
  }
  
  // 检查是否需要滚动到评论区
  if (route.hash === '#comments') {
    nextTick(() => {
      scrollToComments()
    })
  }
})

/**
 * 数据加载
 */
const loadPost = async () => {
  loading.value = true
  error.value = null
  
  try {
    post.value = await postStore.fetchPost(postId.value)
  } catch (err: any) {
    error.value = err.message || '加载说说失败'
    console.error('加载说说失败:', err)
  } finally {
    loading.value = false
  }
}

const loadComments = async (page = 1, append = false) => {
  commentsLoading.value = true
  
  try {
    const response = await commentService.getComments({
      postId: postId.value,
      page,
      limit: commentsPagination.value.itemsPerPage,
      sort: 'createdAt',
      order: 'asc'
    })
    
    if (append) {
      comments.value.push(...response.comments)
    } else {
      comments.value = response.comments
    }
    
    commentsPagination.value = {
      currentPage: response.pagination.page,
      totalPages: response.pagination.totalPages,
      totalItems: response.pagination.total,
      itemsPerPage: response.pagination.limit,
      hasNextPage: response.pagination.page < response.pagination.totalPages,
      hasPrevPage: response.pagination.page > 1
    }
  } catch (error) {
    console.error('加载评论失败:', error)
  } finally {
    commentsLoading.value = false
  }
}

const loadMoreComments = () => {
  if (commentsPagination.value.hasNextPage) {
    loadComments(commentsPagination.value.currentPage + 1, true)
  }
}

/**
 * 事件处理
 */
const goBack = () => {
  router.back()
}

const goToLogin = () => {
  router.push('/login')
}

const handleLike = async () => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录')
    goToLogin()
    return
  }
  
  if (!post.value) return
  
  try {
    const result = await postStore.toggleLike(post.value.id)
    post.value.isLiked = result.action === 'liked'
    post.value.likeCount = result.likeCount
  } catch (error) {
    console.error('点赞操作失败:', error)
  }
}

const handleShare = async () => {
  const url = window.location.href
  
  if (navigator.share) {
    try {
      await navigator.share({
        title: `${post.value?.author?.username}的说说`,
        text: post.value?.content,
        url
      })
    } catch (error) {
      console.error('分享失败:', error)
    }
  } else {
    // 复制链接到剪贴板
    try {
      await navigator.clipboard.writeText(url)
      ElMessage.success('链接已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
    }
  }
}

const handleSubmitComment = async () => {
  if (!commentText.value.trim()) return
  
  submittingComment.value = true
  
  try {
    const commentData: CommentCreateRequest = {
      content: commentText.value.trim(),
      postId: postId.value
    }
    
    const newComment = await commentService.createComment(commentData)
    comments.value.push(newComment)
    commentText.value = ''
    
    // 更新说说的评论数
    if (post.value) {
      post.value.commentCount = (post.value.commentCount || 0) + 1
    }
    
    ElMessage.success('评论发表成功')
  } catch (error) {
    console.error('发表评论失败:', error)
  } finally {
    submittingComment.value = false
  }
}

const handleReply = (comment: Comment) => {
  commentText.value = `@${comment.author?.username} `
  scrollToCommentForm()
}

const handleCommentLike = async (commentId: number) => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录')
    goToLogin()
    return
  }
  
  try {
    await commentService.toggleLike(commentId)
    // 更新评论点赞状态
    const comment = comments.value.find(c => c.id === commentId)
    if (comment) {
      comment.isLiked = !comment.isLiked
      comment.likeCount = (comment.likeCount || 0) + (comment.isLiked ? 1 : -1)
    }
  } catch (error) {
    console.error('评论点赞失败:', error)
  }
}

const handleCommentDelete = async (commentId: number) => {
  try {
    await commentService.deleteComment(commentId)
    comments.value = comments.value.filter(c => c.id !== commentId)
    
    // 更新说说的评论数
    if (post.value) {
      post.value.commentCount = Math.max(0, (post.value.commentCount || 0) - 1)
    }
    
    ElMessage.success('评论删除成功')
  } catch (error) {
    console.error('删除评论失败:', error)
  }
}

/**
 * 工具函数
 */
const scrollToComments = () => {
  const commentsElement = document.getElementById('comments')
  if (commentsElement) {
    commentsElement.scrollIntoView({ behavior: 'smooth' })
  }
}

const scrollToCommentForm = () => {
  const formElement = document.querySelector('.comment-form')
  if (formElement) {
    formElement.scrollIntoView({ behavior: 'smooth' })
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getAvatarUrl = (username?: string) => {
  if (!username) return ''
  return `https://api.dicebear.com/7.x/initials/svg?seed=${username}`
}

const getImageClass = (total: number, index: number) => {
  if (total === 1) return 'image-single'
  if (total === 2) return 'image-double'
  if (total <= 4) return 'image-grid-2'
  return 'image-grid-3'
}
</script>

<style scoped>
.post-detail {
  min-height: 100vh;
  background: var(--el-bg-color-page);
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.back-button {
  margin-bottom: 20px;
}

.post-card {
  margin-bottom: 20px;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-details {
  margin-left: 12px;
}

.author-name {
  font-weight: 500;
  font-size: 16px;
}

.post-time {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.post-body {
  margin-bottom: 20px;
}

.post-text {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 16px;
  white-space: pre-wrap;
}

.post-images {
  display: grid;
  gap: 8px;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.image-single {
  grid-column: 1 / -1;
  max-height: 400px;
}

.image-double {
  grid-template-columns: 1fr 1fr;
  height: 250px;
}

.image-grid-2 {
  grid-template-columns: 1fr 1fr;
  height: 200px;
}

.image-grid-3 {
  grid-template-columns: repeat(3, 1fr);
  height: 150px;
}

.post-location {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.post-actions {
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.comments-section {
  margin-top: 20px;
}

.comments-header h3 {
  margin: 0;
  font-size: 18px;
}

.comment-form {
  margin-bottom: 24px;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.login-prompt {
  margin-bottom: 24px;
}

.loading-container,
.error-container {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .post-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .action-buttons {
    flex-wrap: wrap;
  }
  
  .post-images {
    gap: 4px;
  }
}
</style>
