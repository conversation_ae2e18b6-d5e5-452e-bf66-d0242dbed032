import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { User } from './User'

/**
 * 媒体文件模型的属性接口定义
 */
export interface MediaAttributes {
  id: number
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  width?: number
  height?: number
  uploaderId: number
  category: 'image' | 'video' | 'audio' | 'document'
  tags?: string[]
  description?: string
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 媒体文件创建时的属性接口定义，部分字段为可选
 */
export interface MediaCreationAttributes extends Optional<MediaAttributes, 'id' | 'thumbnailUrl' | 'width' | 'height' | 'tags' | 'description' | 'createdAt' | 'updatedAt'> {}

/**
 * 媒体文件模型类，继承自Sequelize的Model基类
 * 实现了媒体文件的基本操作和关联关系
 */
export class Media extends Model<MediaAttributes, MediaCreationAttributes> implements MediaAttributes {
  public id!: number
  public filename!: string
  public originalName!: string
  public mimeType!: string
  public size!: number
  public url!: string
  public thumbnailUrl?: string
  public width?: number
  public height?: number
  public uploaderId!: number
  public category!: 'image' | 'video' | 'audio' | 'document'
  public tags?: string[]
  public description?: string
  public isPublic!: boolean
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly uploader?: User

  public static associations: {
    uploader: Association<Media, User>
  }

  /**
   * 获取文件扩展名
   */
  public getFileExtension(): string {
    return this.filename.split('.').pop()?.toLowerCase() || ''
  }

  /**
   * 检查是否为图片文件
   */
  public isImage(): boolean {
    return this.category === 'image' || this.mimeType.startsWith('image/')
  }

  /**
   * 检查是否为视频文件
   */
  public isVideo(): boolean {
    return this.category === 'video' || this.mimeType.startsWith('video/')
  }

  /**
   * 检查是否为音频文件
   */
  public isAudio(): boolean {
    return this.category === 'audio' || this.mimeType.startsWith('audio/')
  }

  /**
   * 获取格式化的文件大小
   */
  public getFormattedSize(): string {
    const bytes = this.size
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 根据MIME类型自动确定媒体类别
   */
  public static getCategoryFromMimeType(mimeType: string): 'image' | 'video' | 'audio' | 'document' {
    if (mimeType.startsWith('image/')) return 'image'
    if (mimeType.startsWith('video/')) return 'video'
    if (mimeType.startsWith('audio/')) return 'audio'
    return 'document'
  }

  /**
   * 验证文件类型是否被支持
   */
  public static isSupportedMimeType(mimeType: string): boolean {
    const supportedTypes = [
      // 图片类型
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
      // 视频类型
      'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov',
      // 音频类型
      'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac',
      // 文档类型
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    return supportedTypes.includes(mimeType)
  }
}

/**
 * 初始化媒体文件模型的数据库映射配置
 * 包含字段定义、验证规则等
 */
Media.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    filename: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    originalName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'original_name',
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    mimeType: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'mime_type',
      validate: {
        notEmpty: true,
        isValidMimeType(value: string) {
          if (!Media.isSupportedMimeType(value)) {
            throw new Error(`不支持的文件类型: ${value}`)
          }
        }
      }
    },
    size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
        max: 100 * 1024 * 1024 // 100MB 最大文件大小
      }
    },
    url: {
      type: DataTypes.STRING(500),
      allowNull: false,
      validate: {
        notEmpty: true,
        isUrl: true
      }
    },
    thumbnailUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      field: 'thumbnail_url',
      validate: {
        isUrl: true
      }
    },
    width: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0
      }
    },
    height: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0
      }
    },
    uploaderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'uploader_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    category: {
      type: DataTypes.ENUM('image', 'video', 'audio', 'document'),
      allowNull: false,
      defaultValue: 'image'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      validate: {
        isValidTagArray(value: any) {
          if (value !== null && value !== undefined) {
            if (!Array.isArray(value)) {
              throw new Error('标签必须是数组格式')
            }
            for (const tag of value) {
              if (typeof tag !== 'string' || !tag.trim()) {
                throw new Error('标签必须是有效的字符串')
              }
            }
          }
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_public'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'Media',
    tableName: 'media',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['uploader_id']
      },
      {
        fields: ['category']
      },
      {
        fields: ['mime_type']
      },
      {
        fields: ['is_public']
      },
      {
        fields: ['created_at']
      }
    ],
    hooks: {
      beforeValidate: (media: Media) => {
        // 根据MIME类型自动设置类别
        if (!media.category && media.mimeType) {
          media.category = Media.getCategoryFromMimeType(media.mimeType)
        }
      }
    }
  }
)
