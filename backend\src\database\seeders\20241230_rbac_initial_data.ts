import { QueryInterface } from 'sequelize'

/**
 * RBAC系统初始数据种子文件
 * 创建默认角色和权限，建立基础的权限体系
 */

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 插入默认角色
  await queryInterface.bulkInsert('roles', [
    {
      id: 1,
      name: 'super_admin',
      description: '超级管理员，拥有系统所有权限',
      is_active: true,
      is_system: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      name: 'admin',
      description: '管理员，拥有大部分管理权限',
      is_active: true,
      is_system: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 3,
      name: 'editor',
      description: '编辑者，可以管理内容',
      is_active: true,
      is_system: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 4,
      name: 'user',
      description: '普通用户，基础权限',
      is_active: true,
      is_system: true,
      created_at: new Date(),
      updated_at: new Date()
    }
  ])

  // 插入默认权限
  const permissions = [
    // 用户管理权限
    { name: 'user.create', description: '创建用户', resource: 'user', action: 'create' },
    { name: 'user.read', description: '查看用户', resource: 'user', action: 'read' },
    { name: 'user.update', description: '更新用户', resource: 'user', action: 'update' },
    { name: 'user.delete', description: '删除用户', resource: 'user', action: 'delete' },
    { name: 'user.list', description: '用户列表', resource: 'user', action: 'list' },

    // 文章管理权限
    { name: 'article.create', description: '创建文章', resource: 'article', action: 'create' },
    { name: 'article.read', description: '查看文章', resource: 'article', action: 'read' },
    { name: 'article.update', description: '更新文章', resource: 'article', action: 'update' },
    { name: 'article.delete', description: '删除文章', resource: 'article', action: 'delete' },
    { name: 'article.list', description: '文章列表', resource: 'article', action: 'list' },
    { name: 'article.publish', description: '发布文章', resource: 'article', action: 'publish' },

    // 分类管理权限
    { name: 'category.create', description: '创建分类', resource: 'category', action: 'create' },
    { name: 'category.read', description: '查看分类', resource: 'category', action: 'read' },
    { name: 'category.update', description: '更新分类', resource: 'category', action: 'update' },
    { name: 'category.delete', description: '删除分类', resource: 'category', action: 'delete' },
    { name: 'category.list', description: '分类列表', resource: 'category', action: 'list' },

    // 标签管理权限
    { name: 'tag.create', description: '创建标签', resource: 'tag', action: 'create' },
    { name: 'tag.read', description: '查看标签', resource: 'tag', action: 'read' },
    { name: 'tag.update', description: '更新标签', resource: 'tag', action: 'update' },
    { name: 'tag.delete', description: '删除标签', resource: 'tag', action: 'delete' },
    { name: 'tag.list', description: '标签列表', resource: 'tag', action: 'list' },

    // 评论管理权限
    { name: 'comment.create', description: '创建评论', resource: 'comment', action: 'create' },
    { name: 'comment.read', description: '查看评论', resource: 'comment', action: 'read' },
    { name: 'comment.update', description: '更新评论', resource: 'comment', action: 'update' },
    { name: 'comment.delete', description: '删除评论', resource: 'comment', action: 'delete' },
    { name: 'comment.list', description: '评论列表', resource: 'comment', action: 'list' },
    { name: 'comment.moderate', description: '审核评论', resource: 'comment', action: 'moderate' },

    // 说说管理权限
    { name: 'post.create', description: '创建说说', resource: 'post', action: 'create' },
    { name: 'post.read', description: '查看说说', resource: 'post', action: 'read' },
    { name: 'post.update', description: '更新说说', resource: 'post', action: 'update' },
    { name: 'post.delete', description: '删除说说', resource: 'post', action: 'delete' },
    { name: 'post.list', description: '说说列表', resource: 'post', action: 'list' },

    // 媒体管理权限
    { name: 'media.upload', description: '上传媒体', resource: 'media', action: 'upload' },
    { name: 'media.read', description: '查看媒体', resource: 'media', action: 'read' },
    { name: 'media.update', description: '更新媒体', resource: 'media', action: 'update' },
    { name: 'media.delete', description: '删除媒体', resource: 'media', action: 'delete' },
    { name: 'media.list', description: '媒体列表', resource: 'media', action: 'list' },

    // 角色管理权限
    { name: 'role.create', description: '创建角色', resource: 'role', action: 'create' },
    { name: 'role.read', description: '查看角色', resource: 'role', action: 'read' },
    { name: 'role.update', description: '更新角色', resource: 'role', action: 'update' },
    { name: 'role.delete', description: '删除角色', resource: 'role', action: 'delete' },
    { name: 'role.list', description: '角色列表', resource: 'role', action: 'list' },
    { name: 'role.assign', description: '分配角色', resource: 'role', action: 'assign' },

    // 权限管理权限
    { name: 'permission.read', description: '查看权限', resource: 'permission', action: 'read' },
    { name: 'permission.list', description: '权限列表', resource: 'permission', action: 'list' },
    { name: 'permission.assign', description: '分配权限', resource: 'permission', action: 'assign' },

    // 系统管理权限
    { name: 'system.settings', description: '系统设置', resource: 'system', action: 'settings' },
    { name: 'system.backup', description: '系统备份', resource: 'system', action: 'backup' },
    { name: 'system.logs', description: '系统日志', resource: 'system', action: 'logs' },
    { name: 'system.monitor', description: '系统监控', resource: 'system', action: 'monitor' },

    // 通知管理权限
    { name: 'notification.create', description: '创建通知', resource: 'notification', action: 'create' },
    { name: 'notification.read', description: '查看通知', resource: 'notification', action: 'read' },
    { name: 'notification.update', description: '更新通知', resource: 'notification', action: 'update' },
    { name: 'notification.delete', description: '删除通知', resource: 'notification', action: 'delete' },
    { name: 'notification.list', description: '通知列表', resource: 'notification', action: 'list' }
  ]

  const permissionsWithTimestamps = permissions.map((permission, index) => ({
    id: index + 1,
    ...permission,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  }))

  await queryInterface.bulkInsert('permissions', permissionsWithTimestamps)

  // 为超级管理员分配所有权限
  const superAdminPermissions = permissionsWithTimestamps.map((permission, index) => ({
    id: index + 1,
    role_id: 1, // super_admin
    permission_id: permission.id,
    assigned_at: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  }))

  await queryInterface.bulkInsert('role_permissions', superAdminPermissions)

  // 为管理员分配大部分权限（除了系统级权限）
  const adminPermissionNames = [
    'user.read', 'user.update', 'user.list',
    'article.create', 'article.read', 'article.update', 'article.delete', 'article.list', 'article.publish',
    'category.create', 'category.read', 'category.update', 'category.delete', 'category.list',
    'tag.create', 'tag.read', 'tag.update', 'tag.delete', 'tag.list',
    'comment.create', 'comment.read', 'comment.update', 'comment.delete', 'comment.list', 'comment.moderate',
    'post.create', 'post.read', 'post.update', 'post.delete', 'post.list',
    'media.upload', 'media.read', 'media.update', 'media.delete', 'media.list',
    'role.read', 'role.list',
    'permission.read', 'permission.list',
    'notification.create', 'notification.read', 'notification.update', 'notification.delete', 'notification.list'
  ]

  const adminPermissions = permissionsWithTimestamps
    .filter(p => adminPermissionNames.includes(p.name))
    .map((permission, index) => ({
      id: superAdminPermissions.length + index + 1,
      role_id: 2, // admin
      permission_id: permission.id,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    }))

  await queryInterface.bulkInsert('role_permissions', adminPermissions)

  // 为编辑者分配内容管理权限
  const editorPermissionNames = [
    'article.create', 'article.read', 'article.update', 'article.list', 'article.publish',
    'category.read', 'category.list',
    'tag.create', 'tag.read', 'tag.update', 'tag.list',
    'comment.create', 'comment.read', 'comment.update', 'comment.list',
    'post.create', 'post.read', 'post.update', 'post.list',
    'media.upload', 'media.read', 'media.list'
  ]

  const editorPermissions = permissionsWithTimestamps
    .filter(p => editorPermissionNames.includes(p.name))
    .map((permission, index) => ({
      id: superAdminPermissions.length + adminPermissions.length + index + 1,
      role_id: 3, // editor
      permission_id: permission.id,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    }))

  await queryInterface.bulkInsert('role_permissions', editorPermissions)

  // 为普通用户分配基础权限
  const userPermissionNames = [
    'article.read', 'article.list',
    'category.read', 'category.list',
    'tag.read', 'tag.list',
    'comment.create', 'comment.read',
    'post.create', 'post.read', 'post.update',
    'media.upload', 'media.read',
    'notification.read'
  ]

  const userPermissions = permissionsWithTimestamps
    .filter(p => userPermissionNames.includes(p.name))
    .map((permission, index) => ({
      id: superAdminPermissions.length + adminPermissions.length + editorPermissions.length + index + 1,
      role_id: 4, // user
      permission_id: permission.id,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    }))

  await queryInterface.bulkInsert('role_permissions', userPermissions)
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 删除种子数据（按依赖关系的逆序）
  await queryInterface.bulkDelete('role_permissions', {}, {})
  await queryInterface.bulkDelete('user_roles', {}, {})
  await queryInterface.bulkDelete('permissions', {}, {})
  await queryInterface.bulkDelete('roles', {}, {})
}
