<template>
  <el-dialog
    :model-value="modelValue"
    :title="`管理角色权限 - ${role?.name}`"
    width="800px"
    @update:model-value="$emit('update:modelValue', $event)"
    @open="handleOpen"
  >
    <div v-loading="loading" class="permission-dialog">
      <!-- 权限树 -->
      <div class="permission-tree-container">
        <div class="tree-header">
          <h3>权限列表</h3>
          <div class="tree-actions">
            <el-button size="small" @click="expandAll">
              <el-icon><Plus /></el-icon>
              全部展开
            </el-button>
            <el-button size="small" @click="collapseAll">
              <el-icon><Minus /></el-icon>
              全部收起
            </el-button>
            <el-button size="small" @click="checkAll">
              <el-icon><Check /></el-icon>
              全选
            </el-button>
            <el-button size="small" @click="uncheckAll">
              <el-icon><Close /></el-icon>
              取消全选
            </el-button>
          </div>
        </div>
        
        <el-tree
          ref="permissionTreeRef"
          :data="permissionTreeData"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="checkedPermissions"
          @check="handlePermissionCheck"
          class="permission-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="node-label">{{ data.label }}</span>
              <span v-if="data.description" class="node-description">
                {{ data.description }}
              </span>
            </div>
          </template>
        </el-tree>
      </div>
      
      <!-- 已选权限统计 -->
      <div class="permission-summary">
        <el-card>
          <template #header>
            <span>权限统计</span>
          </template>
          <div class="summary-content">
            <div class="summary-item">
              <span class="label">总权限数：</span>
              <span class="value">{{ totalPermissions }}</span>
            </div>
            <div class="summary-item">
              <span class="label">已选权限：</span>
              <span class="value">{{ selectedPermissions.length }}</span>
            </div>
            <div class="summary-item">
              <span class="label">覆盖率：</span>
              <span class="value">
                {{ totalPermissions > 0 ? Math.round((selectedPermissions.length / totalPermissions) * 100) : 0 }}%
              </span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="$emit('update:modelValue', false)">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存权限
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Minus, Check, Close } from '@element-plus/icons-vue'
import { useRbacStore } from '@/stores/rbac'
import type { Role, Permission } from '@/services/rbac'

// Props & Emits
interface Props {
  modelValue: boolean
  role: Role | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'updated': []
}>()

// Store
const rbacStore = useRbacStore()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const permissionTreeRef = ref()
const allPermissions = ref<Permission[]>([])
const rolePermissions = ref<Permission[]>([])
const selectedPermissions = ref<Permission[]>([])

// 树形结构配置
const treeProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
}

// 计算属性
const permissionTreeData = computed(() => {
  // 将权限按资源分组构建树形结构
  const resourceGroups: Record<string, Permission[]> = {}
  
  allPermissions.value.forEach(permission => {
    if (!resourceGroups[permission.resource]) {
      resourceGroups[permission.resource] = []
    }
    resourceGroups[permission.resource].push(permission)
  })
  
  return Object.keys(resourceGroups).map(resource => ({
    id: `resource_${resource}`,
    label: getResourceLabel(resource),
    resource,
    children: resourceGroups[resource].map(permission => ({
      id: permission.id,
      label: `${permission.action} - ${permission.name}`,
      description: permission.description,
      permission
    }))
  }))
})

const checkedPermissions = computed(() => {
  return rolePermissions.value.map(p => p.id)
})

const totalPermissions = computed(() => {
  return allPermissions.value.length
})

// 方法
const getResourceLabel = (resource: string): string => {
  const resourceLabels: Record<string, string> = {
    'user': '用户管理',
    'article': '文章管理',
    'category': '分类管理',
    'tag': '标签管理',
    'comment': '评论管理',
    'post': '说说管理',
    'media': '媒体管理',
    'role': '角色管理',
    'permission': '权限管理',
    'system': '系统管理',
    'notification': '通知管理'
  }
  return resourceLabels[resource] || resource
}

const loadPermissions = async () => {
  try {
    loading.value = true
    
    // 加载所有权限
    await rbacStore.fetchPermissions({ limit: 1000 })
    allPermissions.value = rbacStore.permissions
    
    // 加载角色权限
    if (props.role) {
      rolePermissions.value = await rbacStore.fetchRolePermissions(props.role.id)
    }
  } catch (error) {
    console.error('加载权限失败:', error)
    ElMessage.error('加载权限失败')
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  if (props.role) {
    loadPermissions()
  }
}

const handlePermissionCheck = (data: any, checked: any) => {
  // 获取当前选中的权限ID列表
  const checkedKeys = permissionTreeRef.value.getCheckedKeys()
  const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
  
  // 过滤出权限节点（非资源分组节点）
  const permissionIds = [...checkedKeys, ...halfCheckedKeys].filter(key => 
    typeof key === 'number'
  )
  
  selectedPermissions.value = allPermissions.value.filter(p => 
    permissionIds.includes(p.id)
  )
}

const expandAll = () => {
  const allNodes = permissionTreeRef.value.store.nodesMap
  Object.keys(allNodes).forEach(key => {
    allNodes[key].expanded = true
  })
}

const collapseAll = () => {
  const allNodes = permissionTreeRef.value.store.nodesMap
  Object.keys(allNodes).forEach(key => {
    allNodes[key].expanded = false
  })
}

const checkAll = () => {
  const allPermissionIds = allPermissions.value.map(p => p.id)
  permissionTreeRef.value.setCheckedKeys(allPermissionIds)
  selectedPermissions.value = [...allPermissions.value]
}

const uncheckAll = () => {
  permissionTreeRef.value.setCheckedKeys([])
  selectedPermissions.value = []
}

const handleSave = async () => {
  if (!props.role) return
  
  try {
    saving.value = true
    
    // 获取当前选中的权限ID
    const currentPermissionIds = selectedPermissions.value.map(p => p.id)
    const originalPermissionIds = rolePermissions.value.map(p => p.id)
    
    // 计算需要添加和移除的权限
    const toAdd = currentPermissionIds.filter(id => !originalPermissionIds.includes(id))
    const toRemove = originalPermissionIds.filter(id => !currentPermissionIds.includes(id))
    
    // 执行权限变更
    if (toAdd.length > 0) {
      await rbacStore.assignPermissionsToRole(props.role.id, toAdd)
    }
    
    if (toRemove.length > 0) {
      await rbacStore.removePermissionsFromRole(props.role.id, toRemove)
    }
    
    ElMessage.success('权限保存成功')
    emit('updated')
    emit('update:modelValue', false)
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  } finally {
    saving.value = false
  }
}

// 监听对话框打开状态
watch(() => props.modelValue, (visible) => {
  if (visible && props.role) {
    loadPermissions()
  }
})
</script>

<style scoped>
.permission-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.permission-tree-container {
  margin-bottom: 24px;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.tree-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.tree-actions {
  display: flex;
  gap: 8px;
}

.permission-tree {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 12px;
}

.tree-node {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.node-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.node-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 2px;
}

.permission-summary {
  margin-top: 16px;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item .label {
  color: var(--el-text-color-regular);
}

.summary-item .value {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
