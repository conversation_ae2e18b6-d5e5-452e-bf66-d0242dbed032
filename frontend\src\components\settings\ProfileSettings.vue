<template>
  <div class="profile-settings">
    <div class="settings-section-header">
      <h3 class="section-title">
        <el-icon><User /></el-icon>
        个人信息
      </h3>
      <p class="section-description">管理您的个人资料信息</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="profile-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 头像设置 -->
      <el-form-item label="头像" prop="avatar">
        <div class="avatar-section">
          <el-avatar
            :size="80"
            :src="formData.avatar || defaultAvatar"
            class="avatar-preview"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="avatar-actions">
            <el-upload
              :show-file-list="false"
              :before-upload="handleAvatarUpload"
              accept="image/*"
              class="avatar-upload"
            >
              <el-button size="small" type="primary">
                <el-icon><Upload /></el-icon>
                上传头像
              </el-button>
            </el-upload>
            <el-button 
              v-if="formData.avatar" 
              size="small" 
              type="danger" 
              plain
              @click="removeAvatar"
            >
              <el-icon><Delete /></el-icon>
              移除
            </el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 显示名称 -->
      <el-form-item label="显示名称" prop="displayName">
        <el-input
          v-model="formData.displayName"
          placeholder="请输入显示名称"
          maxlength="100"
          show-word-limit
          clearable
        />
        <div class="form-tip">
          这是其他用户看到的您的名称
        </div>
      </el-form-item>

      <!-- 个人简介 -->
      <el-form-item label="个人简介" prop="bio">
        <el-input
          v-model="formData.bio"
          type="textarea"
          placeholder="介绍一下您自己..."
          :rows="4"
          maxlength="500"
          show-word-limit
          resize="none"
        />
      </el-form-item>

      <!-- 个人网站 -->
      <el-form-item label="个人网站" prop="website">
        <el-input
          v-model="formData.website"
          placeholder="https://example.com"
          clearable
        >
          <template #prefix>
            <el-icon><Link /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 所在地 -->
      <el-form-item label="所在地" prop="location">
        <el-input
          v-model="formData.location"
          placeholder="请输入您的所在地"
          maxlength="100"
          clearable
        >
          <template #prefix>
            <el-icon><Location /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            保存个人信息
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadRawFile } from 'element-plus'
import { 
  User, 
  Upload, 
  Delete, 
  Link, 
  Location, 
  Check 
} from '@element-plus/icons-vue'

import type { UserSettings, SettingsUpdateParams } from '@/services/settings'

// ==================== Props & Emits ====================

interface Props {
  settings: UserSettings | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  update: [data: SettingsUpdateParams]
}>()

// ==================== 响应式数据 ====================

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  displayName: '',
  avatar: '',
  bio: '',
  website: '',
  location: ''
})

// 默认头像
const defaultAvatar = computed(() => {
  return 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
})

// 表单验证规则
const rules: FormRules = {
  displayName: [
    { max: 100, message: '显示名称不能超过100个字符', trigger: 'blur' }
  ],
  bio: [
    { max: 500, message: '个人简介不能超过500个字符', trigger: 'blur' }
  ],
  website: [
    { 
      pattern: /^https?:\/\/.+/, 
      message: '请输入有效的网站地址（以http://或https://开头）', 
      trigger: 'blur' 
    }
  ],
  location: [
    { max: 100, message: '所在地不能超过100个字符', trigger: 'blur' }
  ]
}

// ==================== 监听器 ====================

// 监听设置数据变化，更新表单
watch(
  () => props.settings,
  (newSettings) => {
    if (newSettings) {
      formData.displayName = newSettings.displayName || ''
      formData.avatar = newSettings.avatar || ''
      formData.bio = newSettings.bio || ''
      formData.website = newSettings.website || ''
      formData.location = newSettings.location || ''
    }
  },
  { immediate: true }
)

// ==================== 方法 ====================

/**
 * 处理头像上传
 */
const handleAvatarUpload = (file: UploadRawFile): boolean => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  // 创建预览URL
  const reader = new FileReader()
  reader.onload = (e) => {
    formData.avatar = e.target?.result as string
  }
  reader.readAsDataURL(file)

  return false // 阻止自动上传
}

/**
 * 移除头像
 */
const removeAvatar = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要移除头像吗？',
      '移除头像',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    formData.avatar = ''
    ElMessage.success('头像已移除')
  } catch (error) {
    // 用户取消操作
  }
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 准备更新数据
    const updateData: SettingsUpdateParams = {
      displayName: formData.displayName || undefined,
      avatar: formData.avatar || undefined,
      bio: formData.bio || undefined,
      website: formData.website || undefined,
      location: formData.location || undefined
    }

    // 移除空值
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof SettingsUpdateParams] === '') {
        delete updateData[key as keyof SettingsUpdateParams]
      }
    })

    emit('update', updateData)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}

/**
 * 重置表单
 */
const handleReset = () => {
  if (props.settings) {
    formData.displayName = props.settings.displayName || ''
    formData.avatar = props.settings.avatar || ''
    formData.bio = props.settings.bio || ''
    formData.website = props.settings.website || ''
    formData.location = props.settings.location || ''
  }
  
  formRef.value?.clearValidate()
  ElMessage.success('表单已重置')
}
</script>

<style scoped>
.profile-settings {
  max-width: 600px;
}

.settings-section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.profile-form {
  margin-top: 24px;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-preview {
  border: 2px solid var(--el-border-color-light);
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.avatar-upload {
  display: block;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .avatar-actions {
    flex-direction: row;
    width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
