<template>
  <div class="notification-preferences">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>通知偏好设置</h3>
          <p class="card-description">
            自定义你希望接收的通知类型和接收方式
          </p>
        </div>
      </template>

      <!-- 加载状态 -->
      <div v-if="loading" class="preferences-loading">
        <el-skeleton :rows="4" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="preferences-error">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
        <el-button @click="loadPreferences" style="margin-top: 16px;">
          重新加载
        </el-button>
      </div>

      <!-- 偏好设置表格 -->
      <div v-else class="preferences-content">
        <el-table 
          :data="preferenceGroups" 
          style="width: 100%"
          :show-header="true"
        >
          <!-- 通知类型列 -->
          <el-table-column label="通知类型" width="200">
            <template #default="{ row }">
              <div class="preference-type">
                <el-icon :size="20" :color="row.config.color">
                  <component :is="getTypeIcon(row.type)" />
                </el-icon>
                <div class="preference-type-info">
                  <div class="preference-type-name">{{ row.config.label }}</div>
                  <div class="preference-type-desc">{{ row.config.description }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 站内通知列 -->
          <el-table-column label="站内通知" align="center" width="120">
            <template #default="{ row }">
              <el-switch
                v-model="row.channels.in_app"
                @change="updatePreference(row.type, 'in_app', $event)"
                :loading="updating"
              />
            </template>
          </el-table-column>

          <!-- 邮件通知列 -->
          <el-table-column label="邮件通知" align="center" width="120">
            <template #default="{ row }">
              <el-switch
                v-model="row.channels.email"
                @change="updatePreference(row.type, 'email', $event)"
                :loading="updating"
              />
            </template>
          </el-table-column>

          <!-- 推送通知列 -->
          <el-table-column label="推送通知" align="center" width="120">
            <template #default="{ row }">
              <el-switch
                v-model="row.channels.push"
                @change="updatePreference(row.type, 'push', $event)"
                :loading="updating"
                :disabled="!pushAvailable"
              />
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" align="center" width="100">
            <template #default="{ row }">
              <el-button
                size="small"
                @click="resetTypePreferences(row.type)"
                :loading="updating"
              >
                重置
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 全局操作 -->
        <div class="preferences-actions">
          <el-button 
            type="primary"
            @click="saveAllPreferences"
            :loading="updating"
          >
            保存设置
          </el-button>
          
          <el-button 
            @click="resetAllPreferences"
            :loading="updating"
          >
            恢复默认
          </el-button>

          <el-button 
            @click="loadPreferences"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>

        <!-- 推送通知设置 -->
        <div class="push-notification-section">
          <el-divider content-position="left">推送通知设置</el-divider>
          
          <div class="push-notification-info">
            <el-alert
              v-if="!pushAvailable"
              title="推送通知暂不可用"
              description="你的浏览器不支持推送通知或者你还没有授权推送权限"
              type="warning"
              show-icon
              :closable="false"
            />
            
            <div v-else class="push-notification-controls">
              <el-button 
                type="success"
                @click="requestPushPermission"
                :loading="requestingPermission"
              >
                启用推送通知
              </el-button>
              
              <el-button 
                @click="sendTestPush"
                :disabled="!pushEnabled"
              >
                发送测试推送
              </el-button>
            </div>
          </div>
        </div>

        <!-- 帮助信息 -->
        <div class="preferences-help">
          <el-divider content-position="left">说明</el-divider>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="站内通知">
              在网站内显示的通知，包括通知中心和顶部提醒
            </el-descriptions-item>
            <el-descriptions-item label="邮件通知">
              发送到你注册邮箱的通知邮件
            </el-descriptions-item>
            <el-descriptions-item label="推送通知">
              浏览器桌面推送通知（需要授权）
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ChatDotRound, 
  Document, 
  Setting, 
  Present 
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import { NOTIFICATION_CONFIG } from '@/types/notification'
import type { 
  NotificationType, 
  NotificationChannel,
  NotificationPreferenceItem 
} from '@/types/notification'

const notificationStore = useNotificationStore()

// 响应式状态
const updating = ref(false)
const requestingPermission = ref(false)
const pushEnabled = ref(false)
const pushAvailable = ref(false)

// 计算属性
const loading = computed(() => notificationStore.preferencesLoading)
const error = computed(() => notificationStore.preferencesError)
const preferences = computed(() => notificationStore.preferences)

// 将偏好设置按类型分组
const preferenceGroups = computed(() => {
  const groups: Array<{
    type: NotificationType
    config: typeof NOTIFICATION_CONFIG.TYPES[NotificationType]
    channels: Record<NotificationChannel, boolean>
  }> = []

  Object.keys(NOTIFICATION_CONFIG.TYPES).forEach(type => {
    const notificationType = type as NotificationType
    const config = NOTIFICATION_CONFIG.TYPES[notificationType]
    
    const channels = {
      in_app: false,
      email: false,
      push: false
    }

    // 从偏好设置中获取当前状态
    preferences.value.forEach(pref => {
      if (pref.notificationType === notificationType) {
        channels[pref.channel] = pref.isEnabled
      }
    })

    groups.push({
      type: notificationType,
      config,
      channels
    })
  })

  return groups
})

// 获取通知类型图标
const getTypeIcon = (type: NotificationType) => {
  const iconMap = {
    interaction: ChatDotRound,
    content: Document,
    system: Setting,
    marketing: Present
  }
  return iconMap[type]
}

// 检查推送通知支持
const checkPushSupport = () => {
  if ('Notification' in window && 'serviceWorker' in navigator) {
    pushAvailable.value = true
    pushEnabled.value = Notification.permission === 'granted'
  }
}

// 请求推送权限
const requestPushPermission = async () => {
  if (!pushAvailable.value) return
  
  try {
    requestingPermission.value = true
    const permission = await Notification.requestPermission()
    
    if (permission === 'granted') {
      pushEnabled.value = true
      ElMessage.success('推送通知权限已授权')
    } else {
      ElMessage.warning('推送通知权限被拒绝')
    }
  } catch (error) {
    ElMessage.error('请求推送权限失败')
  } finally {
    requestingPermission.value = false
  }
}

// 发送测试推送
const sendTestPush = () => {
  if (!pushEnabled.value) return
  
  new Notification('测试通知', {
    body: '这是一条测试推送通知',
    icon: '/favicon.ico'
  })
}

// 更新单个偏好设置
const updatePreference = async (
  type: NotificationType, 
  channel: NotificationChannel, 
  enabled: boolean
) => {
  try {
    updating.value = true
    
    // 构建更新数据
    const updateData: NotificationPreferenceItem[] = [{
      notificationType: type,
      channel,
      isEnabled: enabled
    }]
    
    await notificationStore.updatePreferences(updateData)
  } catch (error) {
    console.error('更新偏好设置失败:', error)
    // 恢复原状态
    await loadPreferences()
  } finally {
    updating.value = false
  }
}

// 重置某个类型的偏好设置
const resetTypePreferences = async (type: NotificationType) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置 ${NOTIFICATION_CONFIG.TYPES[type].label} 的设置吗？`,
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    updating.value = true
    
    // 获取默认设置
    const defaultSettings: NotificationPreferenceItem[] = [
      { notificationType: type, channel: 'in_app', isEnabled: NOTIFICATION_CONFIG.TYPES[type].defaultEnabled },
      { notificationType: type, channel: 'email', isEnabled: false },
      { notificationType: type, channel: 'push', isEnabled: false }
    ]
    
    await notificationStore.updatePreferences(defaultSettings)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置偏好设置失败:', error)
    }
  } finally {
    updating.value = false
  }
}

// 保存所有偏好设置
const saveAllPreferences = async () => {
  try {
    updating.value = true
    
    const allPreferences: NotificationPreferenceItem[] = []
    
    preferenceGroups.value.forEach(group => {
      Object.entries(group.channels).forEach(([channel, enabled]) => {
        allPreferences.push({
          notificationType: group.type,
          channel: channel as NotificationChannel,
          isEnabled: enabled
        })
      })
    })
    
    await notificationStore.updatePreferences(allPreferences)
    ElMessage.success('偏好设置保存成功')
  } catch (error) {
    console.error('保存偏好设置失败:', error)
  } finally {
    updating.value = false
  }
}

// 重置所有偏好设置
const resetAllPreferences = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有通知偏好设置为默认值吗？',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    updating.value = true
    
    const defaultPreferences: NotificationPreferenceItem[] = []
    
    Object.entries(NOTIFICATION_CONFIG.TYPES).forEach(([type, config]) => {
      defaultPreferences.push(
        { notificationType: type as NotificationType, channel: 'in_app', isEnabled: config.defaultEnabled },
        { notificationType: type as NotificationType, channel: 'email', isEnabled: false },
        { notificationType: type as NotificationType, channel: 'push', isEnabled: false }
      )
    })
    
    await notificationStore.updatePreferences(defaultPreferences)
    ElMessage.success('偏好设置已重置为默认值')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置偏好设置失败:', error)
    }
  } finally {
    updating.value = false
  }
}

// 加载偏好设置
const loadPreferences = async () => {
  await notificationStore.fetchPreferences()
}

// 组件挂载时初始化
onMounted(() => {
  checkPushSupport()
  if (preferences.value.length === 0) {
    loadPreferences()
  }
})
</script>

<style scoped>
.notification-preferences {
  max-width: 800px;
  margin: 0 auto;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.card-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.preferences-loading,
.preferences-error {
  padding: 40px 20px;
  text-align: center;
}

.preference-type {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preference-type-info {
  flex: 1;
}

.preference-type-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.preference-type-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.preferences-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
}

.push-notification-section {
  margin-top: 32px;
}

.push-notification-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.preferences-help {
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preferences-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .push-notification-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .preference-type {
    gap: 8px;
  }
  
  .preference-type-name {
    font-size: 14px;
  }
  
  .preference-type-desc {
    font-size: 11px;
  }
}
</style>
