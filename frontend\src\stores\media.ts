import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { MediaService, MediaUtils } from '@/services/media'
import type {
  MediaWithUploader,
  MediaQueryParams,
  MediaStats,
  MediaUpdateAttributes,
  PaginationInfo,
  MediaCategory,
  MediaSortBy,
  SortOrder
} from '@/types/media'

/**
 * 媒体管理状态存储
 * 使用Pinia管理媒体文件的状态和操作
 */
export const useMediaStore = defineStore('media', () => {
  // ==================== 状态定义 ====================

  // 媒体列表
  const mediaList = ref<MediaWithUploader[]>([])

  // 当前选中的媒体
  const currentMedia = ref<MediaWithUploader | null>(null)

  // 分页信息
  const pagination = ref<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false
  })

  // 媒体统计信息
  const stats = ref<MediaStats>({
    total: 0,
    byCategory: {
      image: 0,
      video: 0,
      audio: 0,
      document: 0
    },
    totalSize: 0,
    formattedTotalSize: '0 Bytes'
  })

  // 查询参数
  const queryParams = ref<MediaQueryParams>({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  })

  // 加载状态
  const loading = ref(false)
  const uploading = ref(false)
  const deleting = ref(false)

  // 错误信息
  const error = ref<string | null>(null)

  // 选中的媒体ID列表（用于批量操作）
  const selectedMediaIds = ref<number[]>([])

  // 上传进度
  const uploadProgress = ref<number>(0)

  // ==================== 计算属性 ====================

  // 是否有选中的媒体
  const hasSelectedMedia = computed(() => selectedMediaIds.value.length > 0)

  // 选中的媒体数量
  const selectedCount = computed(() => selectedMediaIds.value.length)

  // 是否为空列表
  const isEmpty = computed(() => mediaList.value.length === 0)

  // 当前页是否有数据
  const hasData = computed(() => !loading.value && !isEmpty.value)

  // 是否可以加载更多
  const canLoadMore = computed(() => pagination.value.hasNextPage)

  // 格式化的统计信息
  const formattedStats = computed(() => ({
    ...stats.value,
    formattedTotalSize: MediaUtils.formatFileSize(stats.value.totalSize)
  }))

  // ==================== 操作方法 ====================

  /**
   * 获取媒体列表
   * @param params 查询参数
   * @param append 是否追加到现有列表（用于分页加载）
   */
  const fetchMediaList = async (params?: Partial<MediaQueryParams>, append = false) => {
    try {
      loading.value = true
      error.value = null

      const mergedParams = { ...queryParams.value, ...params }
      const response = await MediaService.getMediaList(mergedParams)

      if (response.success && response.data) {
        if (append) {
          mediaList.value.push(...response.data.media)
        } else {
          mediaList.value = response.data.media
        }
        pagination.value = response.data.pagination
        queryParams.value = mergedParams
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取媒体列表失败'
      console.error('获取媒体列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取媒体详情
   * @param id 媒体ID
   */
  const fetchMediaDetail = async (id: number) => {
    try {
      loading.value = true
      error.value = null

      const response = await MediaService.getMediaById(id)
      if (response.success && response.data) {
        currentMedia.value = response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取媒体详情失败'
      console.error('获取媒体详情失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取媒体统计信息
   */
  const fetchMediaStats = async () => {
    try {
      const response = await MediaService.getMediaStats()
      if (response.success && response.data) {
        stats.value = response.data
      }
    } catch (err) {
      console.error('获取媒体统计失败:', err)
    }
  }

  /**
   * 上传单个文件
   * @param file 文件对象
   */
  const uploadSingle = async (file: File) => {
    try {
      uploading.value = true
      uploadProgress.value = 0
      error.value = null

      const response = await MediaService.uploadSingle(file, (progress) => {
        uploadProgress.value = progress
      })

      if (response.success) {
        // 刷新媒体列表
        await fetchMediaList()
        await fetchMediaStats()
        return response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '文件上传失败'
      console.error('文件上传失败:', err)
      throw err
    } finally {
      uploading.value = false
      uploadProgress.value = 0
    }
  }

  /**
   * 上传多个文件
   * @param files 文件数组
   */
  const uploadMultiple = async (files: File[]) => {
    try {
      uploading.value = true
      uploadProgress.value = 0
      error.value = null

      const response = await MediaService.uploadMultiple(files, (progress) => {
        uploadProgress.value = progress
      })

      if (response.success) {
        // 刷新媒体列表
        await fetchMediaList()
        await fetchMediaStats()
        return response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '文件上传失败'
      console.error('文件上传失败:', err)
      throw err
    } finally {
      uploading.value = false
      uploadProgress.value = 0
    }
  }

  /**
   * 更新媒体信息
   * @param id 媒体ID
   * @param data 更新数据
   */
  const updateMedia = async (id: number, data: MediaUpdateAttributes) => {
    try {
      loading.value = true
      error.value = null

      const response = await MediaService.updateMedia(id, data)
      if (response.success && response.data) {
        // 更新列表中的对应项
        const index = mediaList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          mediaList.value[index] = response.data
        }

        // 更新当前媒体
        if (currentMedia.value?.id === id) {
          currentMedia.value = response.data
        }

        return response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新媒体信息失败'
      console.error('更新媒体信息失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除媒体文件
   * @param id 媒体ID
   */
  const deleteMedia = async (id: number) => {
    try {
      deleting.value = true
      error.value = null

      const response = await MediaService.deleteMedia(id)
      if (response.success) {
        // 从列表中移除
        mediaList.value = mediaList.value.filter(item => item.id !== id)

        // 清除当前媒体
        if (currentMedia.value?.id === id) {
          currentMedia.value = null
        }

        // 从选中列表中移除
        selectedMediaIds.value = selectedMediaIds.value.filter(mediaId => mediaId !== id)

        // 更新统计信息
        await fetchMediaStats()
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除媒体文件失败'
      console.error('删除媒体文件失败:', err)
      throw err
    } finally {
      deleting.value = false
    }
  }

  /**
   * 批量删除媒体文件
   * @param ids 媒体ID数组
   */
  const batchDeleteMedia = async (ids: number[] = selectedMediaIds.value) => {
    try {
      deleting.value = true
      error.value = null

      const response = await MediaService.batchDelete(ids)
      if (response.success) {
        // 从列表中移除
        mediaList.value = mediaList.value.filter(item => !ids.includes(item.id))

        // 清除选中状态
        selectedMediaIds.value = []

        // 更新统计信息
        await fetchMediaStats()
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量删除失败'
      console.error('批量删除失败:', err)
      throw err
    } finally {
      deleting.value = false
    }
  }

  /**
   * 搜索媒体文件
   * @param keyword 搜索关键词
   * @param filters 过滤条件
   */
  const searchMedia = async (keyword: string, filters?: Partial<MediaQueryParams>) => {
    await fetchMediaList({
      search: keyword,
      page: 1,
      ...filters
    })
  }

  /**
   * 设置排序
   * @param sortBy 排序字段
   * @param sortOrder 排序方向
   */
  const setSorting = async (sortBy: MediaSortBy, sortOrder: SortOrder = 'DESC') => {
    await fetchMediaList({
      sortBy,
      sortOrder,
      page: 1
    })
  }

  /**
   * 设置过滤条件
   * @param category 媒体类别
   */
  const setCategory = async (category?: MediaCategory) => {
    await fetchMediaList({
      category,
      page: 1
    })
  }

  /**
   * 加载下一页
   */
  const loadNextPage = async () => {
    if (canLoadMore.value && !loading.value) {
      await fetchMediaList({
        page: pagination.value.currentPage + 1
      }, true)
    }
  }

  /**
   * 刷新列表
   */
  const refreshList = async () => {
    await fetchMediaList({ page: 1 })
  }

  /**
   * 选择/取消选择媒体
   * @param id 媒体ID
   */
  const toggleSelection = (id: number) => {
    const index = selectedMediaIds.value.indexOf(id)
    if (index > -1) {
      selectedMediaIds.value.splice(index, 1)
    } else {
      selectedMediaIds.value.push(id)
    }
  }

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    if (selectedMediaIds.value.length === mediaList.value.length) {
      selectedMediaIds.value = []
    } else {
      selectedMediaIds.value = mediaList.value.map(item => item.id)
    }
  }

  /**
   * 清除选择
   */
  const clearSelection = () => {
    selectedMediaIds.value = []
  }

  /**
   * 批量更新标签
   * @param ids 媒体ID数组
   * @param tags 标签数组
   */
  const batchUpdateTags = async (ids: number[], tags: string[]) => {
    try {
      loading.value = true
      error.value = null

      // 批量更新每个媒体的标签
      const promises = ids.map(id =>
        MediaService.updateMedia(id, { tags })
      )

      await Promise.all(promises)

      // 更新本地状态
      mediaList.value = mediaList.value.map(media => {
        if (ids.includes(media.id)) {
          return { ...media, tags }
        }
        return media
      })

      // 清除选中状态
      selectedMediaIds.value = []

    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量更新标签失败'
      console.error('批量更新标签失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    mediaList.value = []
    currentMedia.value = null
    selectedMediaIds.value = []
    error.value = null
    queryParams.value = {
      page: 1,
      limit: 20,
      sortBy: 'createdAt',
      sortOrder: 'DESC'
    }
  }

  // ==================== 返回状态和方法 ====================

  return {
    // 状态
    mediaList,
    currentMedia,
    pagination,
    stats,
    queryParams,
    loading,
    uploading,
    deleting,
    error,
    selectedMediaIds,
    uploadProgress,

    // 计算属性
    hasSelectedMedia,
    selectedCount,
    isEmpty,
    hasData,
    canLoadMore,
    formattedStats,

    // 方法
    fetchMediaList,
    fetchMediaDetail,
    fetchMediaStats,
    uploadSingle,
    uploadMultiple,
    updateMedia,
    deleteMedia,
    batchDeleteMedia,
    batchUpdateTags,
    searchMedia,
    setSorting,
    setCategory,
    loadNextPage,
    refreshList,
    toggleSelection,
    toggleSelectAll,
    clearSelection,
    resetState
  }
})
