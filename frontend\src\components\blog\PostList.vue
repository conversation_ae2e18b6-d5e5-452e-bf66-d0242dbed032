<template>
  <div class="post-list">
    <!-- 筛选和搜索栏 -->
    <div v-if="showFilters" class="post-list__filters">
      <div class="filter-row">
        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索说说内容..."
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 可见性筛选 -->
        <el-select
          v-model="visibilityFilter"
          placeholder="可见性"
          clearable
          style="width: 120px"
          @change="handleFilterChange"
        >
          <el-option label="全部" value="" />
          <el-option label="公开" value="public" />
          <el-option label="私密" value="private" />
        </el-select>

        <!-- 排序 -->
        <el-select
          v-model="sortBy"
          placeholder="排序方式"
          style="width: 140px"
          @change="handleFilterChange"
        >
          <el-option label="最新发布" value="createdAt" />
          <el-option label="最多点赞" value="likeCount" />
          <el-option label="最多评论" value="commentCount" />
        </el-select>

        <!-- 刷新按钮 -->
        <el-button
          :icon="Refresh"
          :loading="loading"
          @click="handleRefresh"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && posts.length === 0" class="post-list__loading">
      <PostCard
        v-for="n in skeletonCount"
        :key="n"
        :loading="true"
        :mode="cardMode"
      />
    </div>

    <!-- 说说列表 -->
    <div v-else-if="posts.length > 0" class="post-list__content">
      <PostCard
        v-for="post in posts"
        :key="post.id"
        :post="post"
        :mode="cardMode"
        :show-visibility="showVisibility"
        :show-actions="showActions"
        @click="handlePostClick"
        @like="handleLike"
        @comment="handleComment"
        @edit="handleEdit"
        @delete="handleDelete"
      />

      <!-- 加载更多 -->
      <div v-if="hasMore" class="post-list__load-more">
        <el-button
          v-if="!autoLoad"
          :loading="loading"
          @click="handleLoadMore"
        >
          加载更多
        </el-button>
        <div v-else-if="loading" class="loading-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>正在加载更多...</span>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="post-list__empty">
      <el-empty
        :description="getEmptyDescription()"
        :image-size="120"
      >
        <template v-if="showCreateButton" #extra>
          <el-button type="primary" @click="handleCreate">
            发布说说
          </el-button>
        </template>
      </el-empty>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && pagination.totalPages > 1" class="post-list__pagination">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pagination.itemsPerPage"
        :total="pagination.totalItems"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { Search, Refresh, Loading } from '@element-plus/icons-vue'
import PostCard from './PostCard.vue'
import type { Post, PostParams } from '@/services/types/post'

/**
 * 组件属性定义
 */
interface Props {
  /** 说说列表数据 */
  posts: Post[]
  /** 加载状态 */
  loading?: boolean
  /** 分页信息 */
  pagination?: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  /** 卡片显示模式 */
  cardMode?: 'compact' | 'normal'
  /** 是否显示筛选器 */
  showFilters?: boolean
  /** 是否显示分页 */
  showPagination?: boolean
  /** 是否显示可见性标识 */
  showVisibility?: boolean
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 是否显示创建按钮 */
  showCreateButton?: boolean
  /** 是否自动加载更多 */
  autoLoad?: boolean
  /** 骨架屏数量 */
  skeletonCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  cardMode: 'normal',
  showFilters: true,
  showPagination: true,
  showVisibility: true,
  showActions: false,
  showCreateButton: false,
  autoLoad: false,
  skeletonCount: 5
})

/**
 * 组件事件定义
 */
interface Emits {
  /** 搜索事件 */
  search: [query: string]
  /** 筛选变化事件 */
  'filter-change': [filters: PostParams]
  /** 分页变化事件 */
  'page-change': [page: number]
  /** 页面大小变化事件 */
  'size-change': [size: number]
  /** 加载更多事件 */
  'load-more': []
  /** 刷新事件 */
  refresh: []
  /** 说说点击事件 */
  'post-click': [post: Post]
  /** 点赞事件 */
  like: [postId: number]
  /** 评论事件 */
  comment: [postId: number]
  /** 编辑事件 */
  edit: [post: Post]
  /** 删除事件 */
  delete: [postId: number]
  /** 创建事件 */
  create: []
}

const emit = defineEmits<Emits>()

/**
 * 响应式数据
 */
const searchQuery = ref('')
const visibilityFilter = ref('')
const sortBy = ref('createdAt')
const currentPage = ref(1)

/**
 * 计算属性
 */
const hasMore = computed(() => {
  return props.pagination?.hasNextPage || false
})

/**
 * 防抖搜索
 */
let searchTimer: NodeJS.Timeout | null = null

const handleSearch = (query: string) => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(() => {
    emit('search', query)
  }, 300)
}

/**
 * 事件处理
 */
const handleFilterChange = () => {
  const filters: PostParams = {
    search: searchQuery.value || undefined,
    visibility: visibilityFilter.value as any || undefined,
    sort: sortBy.value,
    order: 'desc'
  }
  emit('filter-change', filters)
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page)
}

const handleSizeChange = (size: number) => {
  emit('size-change', size)
}

const handleLoadMore = () => {
  emit('load-more')
}

const handleRefresh = () => {
  emit('refresh')
}

const handlePostClick = (post: Post) => {
  emit('post-click', post)
}

const handleLike = (postId: number) => {
  emit('like', postId)
}

const handleComment = (postId: number) => {
  emit('comment', postId)
}

const handleEdit = (post: Post) => {
  emit('edit', post)
}

const handleDelete = (postId: number) => {
  emit('delete', postId)
}

const handleCreate = () => {
  emit('create')
}

const getEmptyDescription = () => {
  if (searchQuery.value) {
    return `没有找到包含"${searchQuery.value}"的说说`
  }
  if (visibilityFilter.value) {
    return `没有${visibilityFilter.value === 'public' ? '公开' : '私密'}的说说`
  }
  return '暂无说说'
}

/**
 * 自动加载更多（滚动到底部）
 */
const handleScroll = () => {
  if (!props.autoLoad || props.loading || !hasMore.value) return
  
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight
  
  if (scrollTop + windowHeight >= documentHeight - 100) {
    handleLoadMore()
  }
}

/**
 * 生命周期
 */
onMounted(() => {
  if (props.autoLoad) {
    window.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (props.autoLoad) {
    window.removeEventListener('scroll', handleScroll)
  }
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})

/**
 * 监听器
 */
watch(() => props.pagination?.currentPage, (newPage) => {
  if (newPage && newPage !== currentPage.value) {
    currentPage.value = newPage
  }
})
</script>

<style scoped>
.post-list {
  width: 100%;
}

.post-list__filters {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.post-list__content {
  min-height: 200px;
}

.post-list__load-more {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
}

.post-list__empty {
  padding: 40px 20px;
}

.post-list__pagination {
  display: flex;
  justify-content: center;
  padding: 20px;
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-row > * {
    width: 100% !important;
  }
}
</style>
