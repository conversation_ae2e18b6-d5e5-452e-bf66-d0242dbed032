import { Router } from 'express'
import {
  getPermissions,
  getPermissionById,
  getPermissionsByResource,
  getResources,
  getResourceActions,
  getPermissionRoles,
  createPermission,
  updatePermission,
  deletePermission,
  bulkCreatePermissions
} from '../controllers/permission'
import { authenticateToken } from '../middleware/auth'
import {
  requirePermission,
  requireRole,
  requireAnyRole
} from '../middleware/permission'

/**
 * 权限管理路由
 * 提供权限的查询和管理功能
 */

const router = Router()

/**
 * @route GET /api/permissions
 * @desc 获取权限列表
 * @access 需要 permission.list 权限
 */
router.get('/', 
  authenticateToken,
  requirePermission('permission.list'),
  getPermissions
)

/**
 * @route GET /api/permissions/grouped
 * @desc 根据资源分组获取权限
 * @access 需要 permission.list 权限
 */
router.get('/grouped',
  authenticateToken,
  requirePermission('permission.list'),
  getPermissionsByResource
)

/**
 * @route GET /api/permissions/resources
 * @desc 获取所有资源列表
 * @access 需要 permission.list 权限
 */
router.get('/resources',
  authenticateToken,
  requirePermission('permission.list'),
  getResources
)

/**
 * @route GET /api/permissions/resources/:resource/actions
 * @desc 获取指定资源的所有操作
 * @access 需要 permission.list 权限
 */
router.get('/resources/:resource/actions',
  authenticateToken,
  requirePermission('permission.list'),
  getResourceActions
)

/**
 * @route GET /api/permissions/:id
 * @desc 根据ID获取权限详情
 * @access 需要 permission.read 权限
 */
router.get('/:id',
  authenticateToken,
  requirePermission('permission.read'),
  getPermissionById
)

/**
 * @route POST /api/permissions
 * @desc 创建新权限
 * @access 需要超级管理员权限
 */
router.post('/',
  authenticateToken,
  requireRole('super_admin'),
  createPermission
)

/**
 * @route POST /api/permissions/bulk
 * @desc 批量创建权限
 * @access 需要超级管理员权限
 */
router.post('/bulk',
  authenticateToken,
  requireRole('super_admin'),
  bulkCreatePermissions
)

/**
 * @route PUT /api/permissions/:id
 * @desc 更新权限
 * @access 需要超级管理员权限
 */
router.put('/:id',
  authenticateToken,
  requireRole('super_admin'),
  updatePermission
)

/**
 * @route DELETE /api/permissions/:id
 * @desc 删除权限
 * @access 需要超级管理员权限
 */
router.delete('/:id',
  authenticateToken,
  requireRole('super_admin'),
  deletePermission
)

/**
 * @route GET /api/permissions/:id/roles
 * @desc 获取权限的角色列表
 * @access 需要 permission.read 权限
 */
router.get('/:id/roles',
  authenticateToken,
  requirePermission('permission.read'),
  getPermissionRoles
)

export default router
