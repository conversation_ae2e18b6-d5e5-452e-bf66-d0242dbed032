import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { settingsService, type UserSettings, type SettingsUpdateParams, type UserProfile } from '@/services/settings'
import { useUIStore } from '@/stores/ui'

/**
 * 设置状态管理存储
 * 管理用户设置的状态和操作
 */
export const useSettingsStore = defineStore('settings', () => {
  // ==================== 状态定义 ====================

  // 用户设置
  const settings = ref<UserSettings | null>(null)
  
  // 用户完整信息（包含设置）
  const userProfile = ref<UserProfile | null>(null)
  
  // 默认设置
  const defaultSettings = ref<Partial<UserSettings> | null>(null)
  
  // 加载状态
  const loading = ref(false)
  const saving = ref(false)
  const resetting = ref(false)
  
  // 错误状态
  const error = ref<string | null>(null)
  
  // 设置是否已初始化
  const initialized = ref(false)

  // ==================== 计算属性 ====================

  /**
   * 是否有设置数据
   */
  const hasSettings = computed(() => !!settings.value)

  /**
   * 当前主题
   */
  const currentTheme = computed(() => settings.value?.theme || 'auto')

  /**
   * 当前语言
   */
  const currentLanguage = computed(() => settings.value?.language || 'zh-CN')

  /**
   * 每页显示数量
   */
  const currentItemsPerPage = computed(() => settings.value?.itemsPerPage || 10)

  /**
   * 通知设置摘要
   */
  const notificationSummary = computed(() => {
    if (!settings.value) return { enabled: 0, total: 3 }
    
    const notifications = [
      settings.value.emailNotifications,
      settings.value.commentNotifications,
      settings.value.systemNotifications
    ]
    
    return {
      enabled: notifications.filter(Boolean).length,
      total: notifications.length
    }
  })

  /**
   * 隐私设置摘要
   */
  const privacySummary = computed(() => {
    if (!settings.value) return { isPrivate: false, showEmail: false }
    
    return {
      isPrivate: settings.value.profileVisibility === 'private',
      showEmail: settings.value.showEmail
    }
  })

  /**
   * 安全设置摘要
   */
  const securitySummary = computed(() => {
    if (!settings.value) return { twoFactorEnabled: false }
    
    return {
      twoFactorEnabled: settings.value.twoFactorEnabled
    }
  })

  // ==================== 操作方法 ====================

  /**
   * 获取用户设置
   */
  const fetchSettings = async (): Promise<void> => {
    if (loading.value) return

    loading.value = true
    error.value = null

    try {
      const data = await settingsService.getSettings()
      settings.value = data
      initialized.value = true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取设置失败'
      console.error('获取设置失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新用户设置
   */
  const updateSettings = async (updates: SettingsUpdateParams): Promise<void> => {
    if (saving.value) return

    saving.value = true
    error.value = null

    try {
      const data = await settingsService.updateSettings(updates)
      settings.value = data
      
      // 如果更新了主题，应用到UI
      if (updates.theme) {
        const uiStore = useUIStore()
        uiStore.setTheme(updates.theme)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新设置失败'
      console.error('更新设置失败:', err)
      throw err
    } finally {
      saving.value = false
    }
  }

  /**
   * 重置设置为默认值
   */
  const resetSettings = async (): Promise<void> => {
    if (resetting.value) return

    resetting.value = true
    error.value = null

    try {
      const data = await settingsService.resetSettings()
      settings.value = data
      
      // 重置主题到UI
      const uiStore = useUIStore()
      uiStore.setTheme(data.theme)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '重置设置失败'
      console.error('重置设置失败:', err)
      throw err
    } finally {
      resetting.value = false
    }
  }

  /**
   * 获取用户完整信息
   */
  const fetchUserProfile = async (): Promise<void> => {
    if (loading.value) return

    loading.value = true
    error.value = null

    try {
      const data = await settingsService.getUserProfile()
      userProfile.value = data
      settings.value = data.settings
      initialized.value = true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户信息失败'
      console.error('获取用户信息失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取默认设置
   */
  const fetchDefaultSettings = async (): Promise<void> => {
    try {
      const data = await settingsService.getDefaultSettings()
      defaultSettings.value = data
    } catch (err) {
      console.error('获取默认设置失败:', err)
    }
  }

  /**
   * 批量更新设置
   */
  const batchUpdateSettings = async (settingsArray: SettingsUpdateParams[]): Promise<void> => {
    if (saving.value) return

    saving.value = true
    error.value = null

    try {
      const data = await settingsService.batchUpdateSettings(settingsArray)
      settings.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量更新设置失败'
      console.error('批量更新设置失败:', err)
      throw err
    } finally {
      saving.value = false
    }
  }

  /**
   * 验证设置数据
   */
  const validateSettings = async (settingsData: SettingsUpdateParams): Promise<boolean> => {
    try {
      const result = await settingsService.validateSettings(settingsData)
      return result.success
    } catch (err) {
      console.error('验证设置失败:', err)
      return false
    }
  }

  /**
   * 清除错误状态
   */
  const clearError = (): void => {
    error.value = null
  }

  /**
   * 清除所有状态
   */
  const clearAll = (): void => {
    settings.value = null
    userProfile.value = null
    defaultSettings.value = null
    error.value = null
    initialized.value = false
  }

  /**
   * 初始化设置存储
   */
  const initialize = async (): Promise<void> => {
    if (initialized.value) return

    await Promise.all([
      fetchSettings(),
      fetchDefaultSettings()
    ])
  }

  // ==================== 便捷方法 ====================

  /**
   * 更新个人信息
   */
  const updateProfile = async (profile: {
    displayName?: string
    avatar?: string
    bio?: string
    website?: string
    location?: string
  }): Promise<void> => {
    await updateSettings(profile)
  }

  /**
   * 更新偏好设置
   */
  const updatePreferences = async (preferences: {
    theme?: 'light' | 'dark' | 'auto'
    language?: string
    timezone?: string
    itemsPerPage?: number
  }): Promise<void> => {
    await updateSettings(preferences)
  }

  /**
   * 更新通知设置
   */
  const updateNotifications = async (notifications: {
    emailNotifications?: boolean
    commentNotifications?: boolean
    systemNotifications?: boolean
  }): Promise<void> => {
    await updateSettings(notifications)
  }

  /**
   * 更新隐私设置
   */
  const updatePrivacy = async (privacy: {
    profileVisibility?: 'public' | 'private'
    defaultPostVisibility?: 'public' | 'private'
    showEmail?: boolean
  }): Promise<void> => {
    await updateSettings(privacy)
  }

  /**
   * 更新安全设置
   */
  const updateSecurity = async (security: {
    twoFactorEnabled?: boolean
  }): Promise<void> => {
    await updateSettings(security)
  }

  return {
    // 状态
    settings,
    userProfile,
    defaultSettings,
    loading,
    saving,
    resetting,
    error,
    initialized,
    
    // 计算属性
    hasSettings,
    currentTheme,
    currentLanguage,
    currentItemsPerPage,
    notificationSummary,
    privacySummary,
    securitySummary,
    
    // 操作方法
    fetchSettings,
    updateSettings,
    resetSettings,
    fetchUserProfile,
    fetchDefaultSettings,
    batchUpdateSettings,
    validateSettings,
    clearError,
    clearAll,
    initialize,
    
    // 便捷方法
    updateProfile,
    updatePreferences,
    updateNotifications,
    updatePrivacy,
    updateSecurity
  }
})
