<template>
  <el-card
    :class="[
      'post-card',
      `post-card--${mode}`,
      {
        'post-card--clickable': clickable,
        'post-card--loading': loading,
        'post-card--private': post?.visibility === 'private'
      }
    ]"
    :shadow="loading ? 'never' : 'hover'"
    :tabindex="clickable && !loading ? 0 : -1"
    :role="clickable ? 'button' : 'article'"
    :aria-label="cardAriaLabel"
    :aria-describedby="post ? `post-${post.id}-meta` : undefined"
    @click="handleCardClick"
    @keydown="handleKeydown"
  >
    <!-- 加载状态 -->
    <template v-if="loading">
      <div class="post-card__loading">
        <el-skeleton :rows="mode === 'compact' ? 2 : 4" animated />
      </div>
    </template>

    <!-- 错误状态 -->
    <template v-else-if="error">
      <div class="post-card__error">
        <el-result
          icon="error"
          title="加载失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="emit('retry')">
              重试
            </el-button>
          </template>
        </el-result>
      </div>
    </template>

    <!-- 正常内容 -->
    <template v-else>
      <!-- 可见性标识 -->
      <div v-if="showVisibility && post?.visibility === 'private'" class="post-card__visibility">
        <el-tag type="warning" size="small">
          <el-icon><Lock /></el-icon>
          私密
        </el-tag>
      </div>

      <!-- 紧凑模式 -->
      <div v-if="mode === 'compact'" class="post-card__compact">
        <div class="post-card__content">
          <p class="post-card__text" :title="post?.content">
            {{ truncateText(post?.content, 100) }}
          </p>
          <div class="post-card__meta" :id="post ? `post-${post.id}-meta` : undefined">
            <el-text size="small" type="info">
              {{ formatDate(post?.createdAt) }}
            </el-text>
          </div>
        </div>
      </div>

      <!-- 标准模式 -->
      <div v-else-if="mode === 'normal'" class="post-card__normal">
        <!-- 作者信息 -->
        <div class="post-card__author">
          <el-avatar :size="32" :src="getAvatarUrl(post?.author?.username)">
            {{ post?.author?.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <div class="post-card__author-info">
            <span class="post-card__author-name">{{ post?.author?.username }}</span>
            <span class="post-card__time">{{ formatDate(post?.createdAt) }}</span>
          </div>
        </div>

        <!-- 说说内容 -->
        <div class="post-card__body">
          <p class="post-card__content">{{ post?.content }}</p>
          
          <!-- 图片展示 -->
          <div v-if="post?.images && post.images.length > 0" class="post-card__images">
            <el-image
              v-for="(image, index) in post.images.slice(0, 9)"
              :key="index"
              :src="image"
              :preview-src-list="post.images"
              :initial-index="index"
              fit="cover"
              :class="getImageClass(post.images.length)"
              lazy
            />
          </div>

          <!-- 位置信息 -->
          <div v-if="post?.location" class="post-card__location">
            <el-icon><Location /></el-icon>
            <span>{{ post.location }}</span>
          </div>
        </div>

        <!-- 操作栏 -->
        <div class="post-card__actions">
          <div class="post-card__stats">
            <!-- 点赞 -->
            <el-button
              :type="post?.isLiked ? 'primary' : 'default'"
              :icon="post?.isLiked ? 'HeartFilled' : 'Heart'"
              size="small"
              text
              @click.stop="handleLike"
            >
              {{ post?.likeCount || 0 }}
            </el-button>
            
            <!-- 评论 -->
            <el-button
              type="default"
              :icon="ChatLineRound"
              size="small"
              text
              @click.stop="handleComment"
            >
              {{ post?.commentCount || 0 }}
            </el-button>
          </div>

          <!-- 管理操作 -->
          <div v-if="showActions" class="post-card__management">
            <el-dropdown @command="handleAction">
              <el-button type="default" :icon="MoreFilled" size="small" text />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit" :icon="Edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete" :icon="Delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Lock, 
  Location, 
  ChatLineRound, 
  Edit, 
  Delete, 
  MoreFilled,
  Heart,
  HeartFilled
} from '@element-plus/icons-vue'
import type { Post } from '@/services/types/post'

/**
 * 组件属性定义
 */
interface Props {
  /** 说说数据 */
  post?: Post
  /** 显示模式 */
  mode?: 'compact' | 'normal'
  /** 是否可点击 */
  clickable?: boolean
  /** 是否显示加载状态 */
  loading?: boolean
  /** 错误信息 */
  error?: string
  /** 是否显示可见性标识 */
  showVisibility?: boolean
  /** 是否显示操作按钮 */
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'normal',
  clickable: true,
  loading: false,
  showVisibility: true,
  showActions: false
})

/**
 * 组件事件定义
 */
interface Emits {
  /** 卡片点击事件 */
  click: [post: Post]
  /** 点赞事件 */
  like: [postId: number]
  /** 评论事件 */
  comment: [postId: number]
  /** 编辑事件 */
  edit: [post: Post]
  /** 删除事件 */
  delete: [postId: number]
  /** 重试事件 */
  retry: []
}

const emit = defineEmits<Emits>()

/**
 * 计算属性
 */
const cardAriaLabel = computed(() => {
  if (props.loading) return '正在加载说说'
  if (props.error) return '说说加载失败'
  if (!props.post) return '说说卡片'
  return `${props.post.author?.username}的说说，发布于${formatDate(props.post.createdAt)}`
})

/**
 * 事件处理
 */
const handleCardClick = () => {
  if (props.loading || props.error || !props.clickable || !props.post) return
  emit('click', props.post)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    handleCardClick()
  }
}

const handleLike = () => {
  if (!props.post) return
  emit('like', props.post.id)
}

const handleComment = () => {
  if (!props.post) return
  emit('comment', props.post.id)
}

const handleAction = (command: string) => {
  if (!props.post) return
  
  switch (command) {
    case 'edit':
      emit('edit', props.post)
      break
    case 'delete':
      emit('delete', props.post.id)
      break
  }
}

/**
 * 工具函数
 */
const formatDate = (date?: string) => {
  if (!date) return ''
  const now = new Date()
  const postDate = new Date(date)
  const diff = now.getTime() - postDate.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return postDate.toLocaleDateString('zh-CN')
}

const truncateText = (text?: string, maxLength = 100) => {
  if (!text) return ''
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text
}

const getAvatarUrl = (username?: string) => {
  if (!username) return ''
  // 使用Gravatar或其他头像服务
  return `https://api.dicebear.com/7.x/initials/svg?seed=${username}`
}

const getImageClass = (count: number) => {
  if (count === 1) return 'post-card__image--single'
  if (count === 2) return 'post-card__image--double'
  if (count <= 4) return 'post-card__image--grid-2'
  return 'post-card__image--grid-3'
}
</script>

<style scoped>
.post-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.post-card--clickable {
  cursor: pointer;
}

.post-card--clickable:hover {
  transform: translateY(-2px);
}

.post-card--private {
  border-left: 4px solid var(--el-color-warning);
}

.post-card__visibility {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
}

.post-card__author {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.post-card__author-info {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.post-card__author-name {
  font-weight: 500;
  font-size: 14px;
}

.post-card__time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.post-card__content {
  line-height: 1.6;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.post-card__images {
  display: grid;
  gap: 4px;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.post-card__image--single {
  grid-template-columns: 1fr;
  max-height: 300px;
}

.post-card__image--double {
  grid-template-columns: 1fr 1fr;
  height: 200px;
}

.post-card__image--grid-2 {
  grid-template-columns: 1fr 1fr;
  height: 150px;
}

.post-card__image--grid-3 {
  grid-template-columns: repeat(3, 1fr);
  height: 120px;
}

.post-card__location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-bottom: 12px;
}

.post-card__actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.post-card__stats {
  display: flex;
  gap: 16px;
}

.post-card__compact .post-card__text {
  margin-bottom: 8px;
  color: var(--el-text-color-regular);
}

.post-card__compact .post-card__meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
