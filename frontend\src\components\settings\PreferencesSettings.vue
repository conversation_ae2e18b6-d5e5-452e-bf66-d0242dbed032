<template>
  <div class="preferences-settings">
    <div class="settings-section-header">
      <h3 class="section-title">
        <el-icon><Tools /></el-icon>
        偏好设置
      </h3>
      <p class="section-description">自定义您的使用体验</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="preferences-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 主题设置 -->
      <el-form-item label="主题模式" prop="theme">
        <el-radio-group v-model="formData.theme" class="theme-group">
          <el-radio-button value="light">
            <el-icon><Sunny /></el-icon>
            浅色
          </el-radio-button>
          <el-radio-button value="dark">
            <el-icon><Moon /></el-icon>
            深色
          </el-radio-button>
          <el-radio-button value="auto">
            <el-icon><Monitor /></el-icon>
            跟随系统
          </el-radio-button>
        </el-radio-group>
        <div class="form-tip">
          选择您喜欢的界面主题
        </div>
      </el-form-item>

      <!-- 语言设置 -->
      <el-form-item label="语言" prop="language">
        <el-select
          v-model="formData.language"
          placeholder="选择语言"
          style="width: 200px"
        >
          <el-option
            v-for="lang in languageOptions"
            :key="lang.value"
            :label="lang.label"
            :value="lang.value"
          >
            <span class="language-option">
              <span class="language-flag">{{ lang.flag }}</span>
              <span>{{ lang.label }}</span>
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 时区设置 -->
      <el-form-item label="时区" prop="timezone">
        <el-select
          v-model="formData.timezone"
          placeholder="选择时区"
          filterable
          style="width: 300px"
        >
          <el-option
            v-for="tz in timezoneOptions"
            :key="tz.value"
            :label="tz.label"
            :value="tz.value"
          />
        </el-select>
        <div class="form-tip">
          当前时间：{{ currentTime }}
        </div>
      </el-form-item>

      <!-- 每页显示数量 -->
      <el-form-item label="每页显示" prop="itemsPerPage">
        <el-select
          v-model="formData.itemsPerPage"
          placeholder="选择每页显示数量"
          style="width: 150px"
        >
          <el-option
            v-for="option in itemsPerPageOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <div class="form-tip">
          设置列表页面每页显示的内容数量
        </div>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            保存偏好设置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  Tools, 
  Sunny, 
  Moon, 
  Monitor, 
  Check 
} from '@element-plus/icons-vue'

import type { UserSettings, SettingsUpdateParams } from '@/services/settings'
import { settingsService } from '@/services/settings'

// ==================== Props & Emits ====================

interface Props {
  settings: UserSettings | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  update: [data: SettingsUpdateParams]
}>()

// ==================== 响应式数据 ====================

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  theme: 'auto' as 'light' | 'dark' | 'auto',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  itemsPerPage: 10
})

// 当前时间显示
const currentTime = ref('')
let timeInterval: NodeJS.Timeout | null = null

// 语言选项
const languageOptions = [
  { value: 'zh-CN', label: '简体中文', flag: '🇨🇳' },
  { value: 'zh-TW', label: '繁体中文', flag: '🇹🇼' },
  { value: 'en-US', label: 'English', flag: '🇺🇸' },
  { value: 'ja-JP', label: '日本語', flag: '🇯🇵' },
  { value: 'ko-KR', label: '한국어', flag: '🇰🇷' }
]

// 时区选项
const timezoneOptions = computed(() => {
  return settingsService.getSettingsOptions().timezones
})

// 每页显示数量选项
const itemsPerPageOptions = computed(() => {
  return settingsService.getSettingsOptions().itemsPerPageOptions
})

// 表单验证规则
const rules: FormRules = {
  theme: [
    { required: true, message: '请选择主题模式', trigger: 'change' }
  ],
  language: [
    { required: true, message: '请选择语言', trigger: 'change' }
  ],
  timezone: [
    { required: true, message: '请选择时区', trigger: 'change' }
  ],
  itemsPerPage: [
    { required: true, message: '请选择每页显示数量', trigger: 'change' },
    { type: 'number', min: 5, max: 100, message: '每页显示数量必须在5-100之间', trigger: 'change' }
  ]
}

// ==================== 生命周期 ====================

onMounted(() => {
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// ==================== 监听器 ====================

// 监听设置数据变化，更新表单
watch(
  () => props.settings,
  (newSettings) => {
    if (newSettings) {
      formData.theme = newSettings.theme || 'auto'
      formData.language = newSettings.language || 'zh-CN'
      formData.timezone = newSettings.timezone || 'Asia/Shanghai'
      formData.itemsPerPage = newSettings.itemsPerPage || 10
    }
  },
  { immediate: true }
)

// 监听时区变化，更新时间显示
watch(
  () => formData.timezone,
  () => {
    updateCurrentTime()
  }
)

// ==================== 方法 ====================

/**
 * 更新当前时间显示
 */
const updateCurrentTime = () => {
  try {
    const now = new Date()
    const formatter = new Intl.DateTimeFormat('zh-CN', {
      timeZone: formData.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
    currentTime.value = formatter.format(now)
  } catch (error) {
    currentTime.value = new Date().toLocaleString()
  }
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 准备更新数据
    const updateData: SettingsUpdateParams = {
      theme: formData.theme,
      language: formData.language,
      timezone: formData.timezone,
      itemsPerPage: formData.itemsPerPage
    }

    emit('update', updateData)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}

/**
 * 重置表单
 */
const handleReset = () => {
  if (props.settings) {
    formData.theme = props.settings.theme || 'auto'
    formData.language = props.settings.language || 'zh-CN'
    formData.timezone = props.settings.timezone || 'Asia/Shanghai'
    formData.itemsPerPage = props.settings.itemsPerPage || 10
  }
  
  formRef.value?.clearValidate()
  ElMessage.success('表单已重置')
}
</script>

<style scoped>
.preferences-settings {
  max-width: 600px;
}

.settings-section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.preferences-form {
  margin-top: 24px;
}

.theme-group {
  display: flex;
  gap: 8px;
}

.theme-group .el-radio-button {
  flex: 1;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-flag {
  font-size: 16px;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-group {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
