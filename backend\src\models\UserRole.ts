import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 用户角色关联模型的属性接口，定义了用户角色关联对象的基本字段结构
 */
export interface UserRoleAttributes {
  id: number
  userId: number
  roleId: number
  assignedBy?: number
  assignedAt: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * 用户角色关联创建时的属性接口，继承自 UserRoleAttributes，但允许部分字段为空
 */
export interface UserRoleCreationAttributes extends Optional<UserRoleAttributes, 'id' | 'assignedAt' | 'createdAt' | 'updatedAt'> {}

/**
 * 用户角色关联模型类，用于与数据库中的 user_roles 表进行交互
 * 实现了 UserRoleAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class UserRole extends Model<UserRoleAttributes, UserRoleCreationAttributes> implements UserRoleAttributes {
  public id!: number
  public userId!: number
  public roleId!: number
  public assignedBy?: number
  public assignedAt!: Date
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly user?: any
  public readonly role?: any
  public readonly assigner?: any

  /**
   * 根据用户ID查找用户的所有角色关联
   * @param userId - 用户ID
   * @returns 返回用户角色关联列表
   */
  public static async findByUserId(userId: number): Promise<UserRole[]> {
    return this.findAll({ 
      where: { userId },
      include: ['role']
    })
  }

  /**
   * 根据角色ID查找拥有该角色的所有用户关联
   * @param roleId - 角色ID
   * @returns 返回用户角色关联列表
   */
  public static async findByRoleId(roleId: number): Promise<UserRole[]> {
    return this.findAll({ 
      where: { roleId },
      include: ['user']
    })
  }

  /**
   * 检查用户是否拥有指定角色
   * @param userId - 用户ID
   * @param roleId - 角色ID
   * @returns 返回是否拥有角色
   */
  public static async hasRole(userId: number, roleId: number): Promise<boolean> {
    const count = await this.count({ where: { userId, roleId } })
    return count > 0
  }

  /**
   * 检查用户是否拥有指定名称的角色
   * @param userId - 用户ID
   * @param roleName - 角色名称
   * @returns 返回是否拥有角色
   */
  public static async hasRoleByName(userId: number, roleName: string): Promise<boolean> {
    const Role = sequelize.models.Role
    const count = await this.count({
      where: { userId },
      include: [{
        model: Role,
        as: 'role',
        where: { name: roleName }
      }]
    })
    return count > 0
  }

  /**
   * 为用户分配角色
   * @param userId - 用户ID
   * @param roleId - 角色ID
   * @param assignedBy - 分配者ID（可选）
   * @returns 返回创建的用户角色关联
   */
  public static async assignRole(userId: number, roleId: number, assignedBy?: number): Promise<UserRole> {
    // 检查是否已经存在该关联
    const existing = await this.findOne({ where: { userId, roleId } })
    if (existing) {
      return existing
    }

    return this.create({
      userId,
      roleId,
      assignedBy,
      assignedAt: new Date()
    })
  }

  /**
   * 移除用户的角色
   * @param userId - 用户ID
   * @param roleId - 角色ID
   * @returns 返回删除的记录数
   */
  public static async removeRole(userId: number, roleId: number): Promise<number> {
    return this.destroy({ where: { userId, roleId } })
  }

  /**
   * 批量为用户分配角色
   * @param userId - 用户ID
   * @param roleIds - 角色ID数组
   * @param assignedBy - 分配者ID（可选）
   * @returns 返回操作结果
   */
  public static async assignRoles(userId: number, roleIds: number[], assignedBy?: number): Promise<void> {
    // 删除用户现有的所有角色
    await this.destroy({ where: { userId } })

    // 分配新角色
    if (roleIds.length > 0) {
      const userRoles = roleIds.map(roleId => ({
        userId,
        roleId,
        assignedBy,
        assignedAt: new Date()
      }))
      await this.bulkCreate(userRoles)
    }
  }

  /**
   * 获取用户的所有角色
   * @param userId - 用户ID
   * @returns 返回角色列表
   */
  public static async getUserRoles(userId: number): Promise<any[]> {
    const Role = sequelize.models.Role
    const userRoles = await this.findAll({
      where: { userId },
      include: [{
        model: Role,
        as: 'role',
        where: { isActive: true }
      }]
    })
    
    return userRoles.map(ur => ur.role)
  }

  /**
   * 获取用户的所有权限（通过角色）
   * @param userId - 用户ID
   * @returns 返回权限列表
   */
  public static async getUserPermissions(userId: number): Promise<any[]> {
    const Role = sequelize.models.Role
    const Permission = sequelize.models.Permission
    const RolePermission = sequelize.models.RolePermission

    const userRoles = await this.findAll({
      where: { userId },
      include: [{
        model: Role,
        as: 'role',
        where: { isActive: true },
        include: [{
          model: RolePermission,
          as: 'rolePermissions',
          include: [{
            model: Permission,
            as: 'permission',
            where: { isActive: true }
          }]
        }]
      }]
    })

    const permissions: any[] = []
    const permissionIds = new Set()

    userRoles.forEach(userRole => {
      if (userRole.role && userRole.role.rolePermissions) {
        userRole.role.rolePermissions.forEach((rp: any) => {
          if (rp.permission && !permissionIds.has(rp.permission.id)) {
            permissions.push(rp.permission)
            permissionIds.add(rp.permission.id)
          }
        })
      }
    })

    return permissions
  }

  /**
   * 检查用户是否拥有指定权限
   * @param userId - 用户ID
   * @param permissionName - 权限名称
   * @returns 返回是否拥有权限
   */
  public static async hasPermission(userId: number, permissionName: string): Promise<boolean> {
    const Role = sequelize.models.Role
    const Permission = sequelize.models.Permission
    const RolePermission = sequelize.models.RolePermission

    const count = await this.count({
      where: { userId },
      include: [{
        model: Role,
        as: 'role',
        where: { isActive: true },
        include: [{
          model: RolePermission,
          as: 'rolePermissions',
          include: [{
            model: Permission,
            as: 'permission',
            where: { 
              name: permissionName,
              isActive: true 
            }
          }]
        }]
      }]
    })

    return count > 0
  }

  /**
   * 获取角色的用户数量
   * @param roleId - 角色ID
   * @returns 返回用户数量
   */
  public static async getRoleUserCount(roleId: number): Promise<number> {
    return this.count({ where: { roleId } })
  }

  /**
   * 序列化用户角色关联信息，用于API响应
   * @returns 返回序列化后的用户角色关联信息
   */
  public toJSON(): Omit<UserRoleAttributes, never> {
    const values = { ...this.get() }
    return values
  }
}

/**
 * 初始化 UserRole 模型，配置其字段、验证规则和索引
 */
UserRole.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: '用户ID'
    },
    roleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'role_id',
      references: {
        model: 'roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: '角色ID'
    },
    assignedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'assigned_by',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: '分配者ID'
    },
    assignedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'assigned_at',
      comment: '分配时间'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'UserRole',
    tableName: 'user_roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'role_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['role_id']
      },
      {
        fields: ['assigned_by']
      },
      {
        fields: ['assigned_at']
      }
    ]
  }
)
