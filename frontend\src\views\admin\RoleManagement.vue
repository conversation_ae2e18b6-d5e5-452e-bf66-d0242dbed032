<template>
  <div class="role-management">
    <div class="page-header">
      <h1>角色管理</h1>
      <p>管理系统角色和权限分配</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="search-section">
        <el-input
          v-model="searchForm.search"
          placeholder="搜索角色名称或描述"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.isActive"
          placeholder="状态"
          style="width: 120px; margin-left: 12px"
          clearable
        >
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
        
        <el-select
          v-model="searchForm.isSystem"
          placeholder="类型"
          style="width: 120px; margin-left: 12px"
          clearable
        >
          <el-option label="系统角色" :value="true" />
          <el-option label="自定义角色" :value="false" />
        </el-select>
        
        <el-button type="primary" @click="handleSearch" style="margin-left: 12px">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="button-section">
        <el-button
          type="danger"
          :disabled="selectedRoles.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新建角色
        </el-button>
      </div>
    </div>

    <!-- 角色表格 -->
    <div class="table-container">
      <el-table
        v-loading="rbacStore.loading.roles"
        :data="rbacStore.roles"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="角色名称" min-width="150">
          <template #default="{ row }">
            <div class="role-name">
              <span>{{ row.name }}</span>
              <el-tag v-if="row.isSystem" type="info" size="small" style="margin-left: 8px">
                系统角色
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="userCount" label="用户数量" width="100" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewUsers(row)">
              {{ row.userCount || 0 }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="isActive" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              :disabled="row.isSystem"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewPermissions(row)"
            >
              权限
            </el-button>
            
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              :disabled="row.isSystem"
            >
              编辑
            </el-button>
            
            <el-popconfirm
              title="确定要删除这个角色吗？"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="small"
                  :disabled="row.isSystem"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="rbacStore.rolesPagination.page"
          v-model:page-size="rbacStore.rolesPagination.limit"
          :total="rbacStore.rolesPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑角色' : '新建角色'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="roleForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="roleForm.name"
            placeholder="请输入角色名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            placeholder="请输入角色描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="roleForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <RolePermissionDialog
      v-model="permissionDialogVisible"
      :role="selectedRole"
      @updated="handlePermissionUpdated"
    />

    <!-- 用户列表对话框 -->
    <RoleUsersDialog
      v-model="usersDialogVisible"
      :role="selectedRole"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Delete, Plus } from '@element-plus/icons-vue'
import { useRbacStore } from '@/stores/rbac'
import { formatDate } from '@/utils/date'
import type { Role, CreateRoleData, UpdateRoleData } from '@/services/rbac'
import RolePermissionDialog from '@/components/admin/RolePermissionDialog.vue'
import RoleUsersDialog from '@/components/admin/RoleUsersDialog.vue'

// Store
const rbacStore = useRbacStore()

// 响应式数据
const searchForm = reactive({
  search: '',
  isActive: undefined as boolean | undefined,
  isSystem: undefined as boolean | undefined
})

const selectedRoles = ref<Role[]>([])
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const usersDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const selectedRole = ref<Role | null>(null)

// 表单相关
const formRef = ref()
const roleForm = reactive<CreateRoleData & { id?: number }>({
  name: '',
  description: '',
  isActive: true
})

const formRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 方法
const loadRoles = async () => {
  await rbacStore.fetchRoles({
    page: rbacStore.rolesPagination.page,
    limit: rbacStore.rolesPagination.limit,
    ...searchForm
  })
}

const handleSearch = () => {
  rbacStore.rolesPagination.page = 1
  loadRoles()
}

const handleReset = () => {
  searchForm.search = ''
  searchForm.isActive = undefined
  searchForm.isSystem = undefined
  rbacStore.rolesPagination.page = 1
  loadRoles()
}

const handleSelectionChange = (selection: Role[]) => {
  selectedRoles.value = selection
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRoles.value.length} 个角色吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRoles.value.map(role => role.id)
    const success = await rbacStore.batchDeleteRoles(ids)
    if (success) {
      selectedRoles.value = []
      loadRoles()
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleCreate = () => {
  isEdit.value = false
  roleForm.name = ''
  roleForm.description = ''
  roleForm.isActive = true
  delete roleForm.id
  dialogVisible.value = true
}

const handleEdit = (role: Role) => {
  isEdit.value = true
  roleForm.id = role.id
  roleForm.name = role.name
  roleForm.description = role.description || ''
  roleForm.isActive = role.isActive
  dialogVisible.value = true
}

const handleDelete = async (role: Role) => {
  const success = await rbacStore.deleteRole(role.id)
  if (success) {
    loadRoles()
  }
}

const handleStatusChange = async (role: Role) => {
  const success = await rbacStore.updateRole(role.id, { isActive: role.isActive })
  if (!success) {
    // 如果更新失败，恢复原状态
    role.isActive = !role.isActive
  }
}

const handleViewPermissions = (role: Role) => {
  selectedRole.value = role
  permissionDialogVisible.value = true
}

const handleViewUsers = (role: Role) => {
  selectedRole.value = role
  usersDialogVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    let success = false
    if (isEdit.value && roleForm.id) {
      const { id, ...updateData } = roleForm
      success = !!(await rbacStore.updateRole(id, updateData as UpdateRoleData))
    } else {
      success = !!(await rbacStore.createRole(roleForm as CreateRoleData))
    }
    
    if (success) {
      dialogVisible.value = false
      loadRoles()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const handlePermissionUpdated = () => {
  loadRoles()
}

const handlePageChange = (page: number) => {
  rbacStore.rolesPagination.page = page
  loadRoles()
}

const handleSizeChange = (size: number) => {
  rbacStore.rolesPagination.limit = size
  rbacStore.rolesPagination.page = 1
  loadRoles()
}

// 生命周期
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.role-management {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
}

.search-section {
  display: flex;
  align-items: center;
}

.button-section {
  display: flex;
  gap: 12px;
}

.table-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
}

.role-name {
  display: flex;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style>
