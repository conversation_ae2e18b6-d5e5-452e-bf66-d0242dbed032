import { Router } from 'express'
import {
  getSettings,
  updateSettings,
  resetSettings,
  getUserProfile,
  getDefaultSettings,
  validateSettingsData
} from '../controllers/settings'
import { authenticateToken } from '../middleware/auth'
import { settingsValidationMiddleware } from '../middleware/settingsValidation'

const router = Router()

/**
 * 设置相关路由配置
 * 所有路由都以 /api/settings 为前缀
 */

// 公开路由（不需要认证）
/**
 * GET /api/settings/defaults
 * 获取默认设置值
 * 用于前端初始化设置表单的默认值
 */
router.get('/defaults', getDefaultSettings)

/**
 * POST /api/settings/validate
 * 验证设置数据
 * 用于前端实时验证设置数据的有效性
 * 请求体：设置数据对象
 */
router.post('/validate', validateSettingsData)

// 需要认证的路由
/**
 * GET /api/settings
 * 获取当前用户的设置
 * 需要认证
 * 如果用户没有设置记录，会自动创建默认设置
 */
router.get('/', authenticateToken, getSettings)

/**
 * PUT /api/settings
 * 更新当前用户的设置
 * 需要认证
 * 请求体：
 * - displayName: 显示名称（可选）
 * - avatar: 头像URL（可选）
 * - bio: 个人简介（可选）
 * - website: 个人网站（可选）
 * - location: 所在地（可选）
 * - theme: 主题偏好（light/dark/auto，可选）
 * - language: 语言设置（可选）
 * - timezone: 时区设置（可选）
 * - itemsPerPage: 每页显示数量（可选）
 * - emailNotifications: 邮件通知（可选）
 * - commentNotifications: 评论通知（可选）
 * - systemNotifications: 系统通知（可选）
 * - profileVisibility: 个人资料可见性（public/private，可选）
 * - defaultPostVisibility: 默认文章可见性（public/private，可选）
 * - showEmail: 显示邮箱地址（可选）
 * - twoFactorEnabled: 两步验证（可选）
 */
router.put('/', authenticateToken, ...settingsValidationMiddleware, updateSettings)

/**
 * POST /api/settings/reset
 * 重置当前用户的设置为默认值
 * 需要认证
 * 会将所有设置恢复为系统默认值
 */
router.post('/reset', authenticateToken, resetSettings)

/**
 * GET /api/settings/profile
 * 获取当前用户的完整信息（包含设置）
 * 需要认证
 * 返回用户基本信息和设置信息的完整对象
 */
router.get('/profile', authenticateToken, getUserProfile)

export default router
