<template>
  <div class="notification-badge">
    <router-link 
      to="/admin/notifications" 
      class="notification-link"
      :title="`${unreadCount > 0 ? `${unreadCount} 条未读通知` : '通知中心'}`"
    >
      <el-badge 
        :value="unreadCount" 
        :max="99"
        :hidden="unreadCount === 0"
        class="notification-badge-wrapper"
      >
        <el-icon 
          :size="20" 
          :class="['notification-icon', { 'has-unread': unreadCount > 0 }]"
        >
          <Bell />
        </el-icon>
      </el-badge>
    </router-link>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { Bell } from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import { useAuthStore } from '@/stores/auth'

const notificationStore = useNotificationStore()
const authStore = useAuthStore()

// 计算属性
const unreadCount = computed(() => notificationStore.unreadCount)

// 定时刷新未读通知数量
let refreshTimer: NodeJS.Timeout | null = null

const startRefreshTimer = () => {
  // 每30秒刷新一次未读通知数量
  refreshTimer = setInterval(() => {
    if (authStore.isAuthenticated) {
      notificationStore.fetchUnreadCount()
    }
  }, 30000)
}

const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 组件挂载时初始化
onMounted(async () => {
  if (authStore.isAuthenticated) {
    // 获取初始未读通知数量
    await notificationStore.fetchUnreadCount()
    // 启动定时刷新
    startRefreshTimer()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopRefreshTimer()
})
</script>

<style scoped>
.notification-badge {
  display: inline-block;
}

.notification-link {
  display: inline-block;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.notification-link:hover {
  transform: scale(1.1);
}

.notification-badge-wrapper {
  display: inline-block;
}

.notification-icon {
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
  cursor: pointer;
}

.notification-icon:hover {
  color: var(--el-color-primary);
}

.notification-icon.has-unread {
  color: var(--el-color-primary);
  animation: pulse 2s infinite;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-icon {
    font-size: 18px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .notification-icon {
    color: var(--el-text-color-regular);
  }
  
  .notification-icon:hover,
  .notification-icon.has-unread {
    color: var(--el-color-primary);
  }
}

/* Element Plus Badge 样式覆盖 */
:deep(.el-badge__content) {
  background-color: var(--el-color-danger);
  border: 1px solid var(--el-bg-color);
  font-size: 11px;
  min-width: 16px;
  height: 16px;
  line-height: 14px;
  padding: 0 4px;
}

:deep(.el-badge__content.is-fixed) {
  top: 0;
  right: 6px;
}
</style>
