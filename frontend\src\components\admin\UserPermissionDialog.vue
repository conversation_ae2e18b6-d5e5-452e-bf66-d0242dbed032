<template>
  <el-dialog
    :model-value="modelValue"
    :title="`用户权限详情 - ${userRole?.user?.username}`"
    width="700px"
    @update:model-value="$emit('update:modelValue', $event)"
    @open="handleOpen"
  >
    <div v-loading="loading" class="user-permission-dialog">
      <!-- 用户信息 -->
      <div class="user-info-section">
        <el-card>
          <template #header>
            <span>用户信息</span>
          </template>
          <div class="user-details">
            <div class="detail-item">
              <span class="label">用户名：</span>
              <span class="value">{{ userRole?.user?.username }}</span>
            </div>
            <div class="detail-item">
              <span class="label">邮箱：</span>
              <span class="value">{{ userRole?.user?.email }}</span>
            </div>
            <div class="detail-item">
              <span class="label">昵称：</span>
              <span class="value">{{ userRole?.user?.nickname || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">当前角色：</span>
              <span class="value">
                <el-tag
                  :type="userRole?.role?.isSystem ? 'info' : 'primary'"
                  size="small"
                >
                  {{ userRole?.role?.name }}
                </el-tag>
              </span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 角色权限 -->
      <div class="permissions-section">
        <el-card>
          <template #header>
            <div class="permissions-header">
              <span>角色权限</span>
              <el-tag type="info" size="small">
                共 {{ rolePermissions.length }} 个权限
              </el-tag>
            </div>
          </template>
          
          <div v-if="rolePermissions.length === 0" class="empty-permissions">
            <el-empty description="该角色暂无权限" />
          </div>
          
          <div v-else class="permissions-grid">
            <div
              v-for="group in permissionGroups"
              :key="group.resource"
              class="permission-group"
            >
              <div class="group-header">
                <h4>{{ getResourceLabel(group.resource) }}</h4>
                <el-tag size="small" type="primary">
                  {{ group.permissions.length }} 个权限
                </el-tag>
              </div>
              
              <div class="permission-list">
                <el-tag
                  v-for="permission in group.permissions"
                  :key="permission.id"
                  class="permission-tag"
                  size="small"
                  :title="permission.description"
                >
                  {{ permission.action }} - {{ permission.name }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 权限统计 -->
      <div class="statistics-section">
        <el-card>
          <template #header>
            <span>权限统计</span>
          </template>
          <div class="statistics-grid">
            <div class="stat-item">
              <div class="stat-value">{{ rolePermissions.length }}</div>
              <div class="stat-label">总权限数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ permissionGroups.length }}</div>
              <div class="stat-label">涉及模块</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ readPermissions.length }}</div>
              <div class="stat-label">读取权限</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ writePermissions.length }}</div>
              <div class="stat-label">写入权限</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRbacStore } from '@/stores/rbac'
import type { UserRole, Permission } from '@/services/rbac'

// Props & Emits
interface Props {
  modelValue: boolean
  userRole: UserRole | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// Store
const rbacStore = useRbacStore()

// 响应式数据
const loading = ref(false)
const rolePermissions = ref<Permission[]>([])

// 计算属性
const permissionGroups = computed(() => {
  const groups: Record<string, Permission[]> = {}
  
  rolePermissions.value.forEach(permission => {
    if (!groups[permission.resource]) {
      groups[permission.resource] = []
    }
    groups[permission.resource].push(permission)
  })
  
  return Object.keys(groups).map(resource => ({
    resource,
    permissions: groups[resource]
  }))
})

const readPermissions = computed(() => {
  return rolePermissions.value.filter(p => 
    p.action === 'read' || p.action === 'view' || p.action === 'list'
  )
})

const writePermissions = computed(() => {
  return rolePermissions.value.filter(p => 
    ['create', 'update', 'delete', 'manage'].includes(p.action)
  )
})

// 方法
const getResourceLabel = (resource: string): string => {
  const resourceLabels: Record<string, string> = {
    'user': '用户管理',
    'article': '文章管理',
    'category': '分类管理',
    'tag': '标签管理',
    'comment': '评论管理',
    'post': '说说管理',
    'media': '媒体管理',
    'role': '角色管理',
    'permission': '权限管理',
    'system': '系统管理',
    'notification': '通知管理'
  }
  return resourceLabels[resource] || resource
}

const loadRolePermissions = async () => {
  if (!props.userRole?.role) return
  
  try {
    loading.value = true
    rolePermissions.value = await rbacStore.fetchRolePermissions(props.userRole.role.id)
  } catch (error) {
    console.error('加载角色权限失败:', error)
    ElMessage.error('加载角色权限失败')
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  if (props.userRole) {
    loadRolePermissions()
  }
}

// 监听对话框打开状态
watch(() => props.modelValue, (visible) => {
  if (visible && props.userRole) {
    loadRolePermissions()
  }
})
</script>

<style scoped>
.user-permission-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.user-info-section {
  margin-bottom: 24px;
}

.user-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin-right: 8px;
  min-width: 60px;
}

.detail-item .value {
  color: var(--el-text-color-primary);
}

.permissions-section {
  margin-bottom: 24px;
}

.permissions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-permissions {
  text-align: center;
  padding: 40px 0;
}

.permissions-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.permission-group {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.group-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  cursor: help;
}

.statistics-section {
  margin-bottom: 16px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}
</style>
