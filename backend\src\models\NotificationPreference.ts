import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { User } from './User'

/**
 * 通知偏好设置模型的属性接口定义
 */
export interface NotificationPreferenceAttributes {
  id: number
  userId: number
  notificationType: 'interaction' | 'content' | 'system' | 'marketing'
  channel: 'in_app' | 'email' | 'push'
  isEnabled: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 通知偏好设置创建时的属性接口定义，部分字段为可选
 */
export interface NotificationPreferenceCreationAttributes extends Optional<NotificationPreferenceAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

/**
 * 通知偏好设置模型类，继承自Sequelize的Model基类
 */
export class NotificationPreference extends Model<NotificationPreferenceAttributes, NotificationPreferenceCreationAttributes> implements NotificationPreferenceAttributes {
  public id!: number
  public userId!: number
  public notificationType!: 'interaction' | 'content' | 'system' | 'marketing'
  public channel!: 'in_app' | 'email' | 'push'
  public isEnabled!: boolean
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly user?: User

  public static associations: {
    user: Association<NotificationPreference, User>
  }

  /**
   * 获取用户的通知偏好设置
   */
  public static async getUserPreferences(userId: number): Promise<NotificationPreference[]> {
    return await NotificationPreference.findAll({
      where: { userId },
      order: [['notificationType', 'ASC'], ['channel', 'ASC']]
    })
  }

  /**
   * 检查用户是否启用了特定类型和渠道的通知
   */
  public static async isNotificationEnabled(
    userId: number,
    notificationType: 'interaction' | 'content' | 'system' | 'marketing',
    channel: 'in_app' | 'email' | 'push'
  ): Promise<boolean> {
    const preference = await NotificationPreference.findOne({
      where: {
        userId,
        notificationType,
        channel
      }
    })

    // 如果没有设置偏好，默认启用站内通知，其他渠道默认关闭
    if (!preference) {
      return channel === 'in_app'
    }

    return preference.isEnabled
  }

  /**
   * 更新用户的通知偏好设置
   */
  public static async updateUserPreference(
    userId: number,
    notificationType: 'interaction' | 'content' | 'system' | 'marketing',
    channel: 'in_app' | 'email' | 'push',
    isEnabled: boolean
  ): Promise<NotificationPreference> {
    const [preference] = await NotificationPreference.upsert({
      userId,
      notificationType,
      channel,
      isEnabled
    })

    return preference
  }

  /**
   * 批量更新用户的通知偏好设置
   */
  public static async updateUserPreferences(
    userId: number,
    preferences: Array<{
      notificationType: 'interaction' | 'content' | 'system' | 'marketing'
      channel: 'in_app' | 'email' | 'push'
      isEnabled: boolean
    }>
  ): Promise<NotificationPreference[]> {
    const results: NotificationPreference[] = []

    for (const pref of preferences) {
      const [preference] = await NotificationPreference.upsert({
        userId,
        ...pref
      })
      results.push(preference)
    }

    return results
  }

  /**
   * 初始化用户的默认通知偏好设置
   */
  public static async initializeDefaultPreferences(userId: number): Promise<NotificationPreference[]> {
    const defaultPreferences = [
      // 互动通知 - 默认启用站内通知
      { notificationType: 'interaction' as const, channel: 'in_app' as const, isEnabled: true },
      { notificationType: 'interaction' as const, channel: 'email' as const, isEnabled: false },
      { notificationType: 'interaction' as const, channel: 'push' as const, isEnabled: false },

      // 内容通知 - 默认启用站内通知
      { notificationType: 'content' as const, channel: 'in_app' as const, isEnabled: true },
      { notificationType: 'content' as const, channel: 'email' as const, isEnabled: false },
      { notificationType: 'content' as const, channel: 'push' as const, isEnabled: false },

      // 系统通知 - 默认全部启用
      { notificationType: 'system' as const, channel: 'in_app' as const, isEnabled: true },
      { notificationType: 'system' as const, channel: 'email' as const, isEnabled: true },
      { notificationType: 'system' as const, channel: 'push' as const, isEnabled: false },

      // 营销通知 - 默认关闭
      { notificationType: 'marketing' as const, channel: 'in_app' as const, isEnabled: false },
      { notificationType: 'marketing' as const, channel: 'email' as const, isEnabled: false },
      { notificationType: 'marketing' as const, channel: 'push' as const, isEnabled: false }
    ]

    const preferences = defaultPreferences.map(pref => ({
      userId,
      ...pref
    }))

    return await NotificationPreference.bulkCreate(preferences, {
      ignoreDuplicates: true
    })
  }

  /**
   * 获取启用了特定通知类型和渠道的用户ID列表
   */
  public static async getEnabledUsers(
    notificationType: 'interaction' | 'content' | 'system' | 'marketing',
    channel: 'in_app' | 'email' | 'push'
  ): Promise<number[]> {
    const preferences = await NotificationPreference.findAll({
      where: {
        notificationType,
        channel,
        isEnabled: true
      },
      attributes: ['userId']
    })

    return preferences.map(pref => pref.userId)
  }

  /**
   * 重置用户的通知偏好设置为默认值
   */
  public static async resetToDefaults(userId: number): Promise<void> {
    // 删除现有设置
    await NotificationPreference.destroy({
      where: { userId }
    })

    // 重新初始化默认设置
    await this.initializeDefaultPreferences(userId)
  }
}

/**
 * 初始化通知偏好设置模型的数据库映射配置
 */
NotificationPreference.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    notificationType: {
      type: DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
      allowNull: false,
      field: 'notification_type',
      validate: {
        isIn: [['interaction', 'content', 'system', 'marketing']]
      }
    },
    channel: {
      type: DataTypes.ENUM('in_app', 'email', 'push'),
      allowNull: false,
      defaultValue: 'in_app',
      validate: {
        isIn: [['in_app', 'email', 'push']]
      }
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_enabled'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'NotificationPreference',
    tableName: 'notification_preferences',
    timestamps: true,
    indexes: [
      {
        name: 'unique_user_type_channel',
        unique: true,
        fields: ['user_id', 'notification_type', 'channel']
      },
      {
        name: 'idx_user_enabled',
        fields: ['user_id', 'is_enabled']
      },
      {
        name: 'idx_type_channel_enabled',
        fields: ['notification_type', 'channel', 'is_enabled']
      }
    ]
  }
)
