import { Router } from 'express'
import {
  getRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getRolePermissions,
  assignRolePermissions,
  getRoleUsers
} from '../controllers/role'
import { authenticateToken } from '../middleware/auth'
import {
  requirePermission,
  requireRole,
  requireAnyRole
} from '../middleware/permission'

/**
 * 角色管理路由
 * 提供角色的CRUD操作和权限分配功能
 */

const router = Router()

/**
 * @route GET /api/roles
 * @desc 获取角色列表
 * @access 需要 role.list 权限
 */
router.get('/', 
  authenticateToken,
  requirePermission('role.list'),
  getRoles
)

/**
 * @route GET /api/roles/:id
 * @desc 根据ID获取角色详情
 * @access 需要 role.read 权限
 */
router.get('/:id',
  authenticateToken,
  requirePermission('role.read'),
  getRoleById
)

/**
 * @route POST /api/roles
 * @desc 创建新角色
 * @access 需要 role.create 权限
 */
router.post('/',
  authenticateToken,
  requirePermission('role.create'),
  createRole
)

/**
 * @route PUT /api/roles/:id
 * @desc 更新角色
 * @access 需要 role.update 权限
 */
router.put('/:id',
  authenticateToken,
  requirePermission('role.update'),
  updateRole
)

/**
 * @route DELETE /api/roles/:id
 * @desc 删除角色
 * @access 需要 role.delete 权限
 */
router.delete('/:id',
  authenticateToken,
  requirePermission('role.delete'),
  deleteRole
)

/**
 * @route GET /api/roles/:id/permissions
 * @desc 获取角色的权限列表
 * @access 需要 role.read 权限
 */
router.get('/:id/permissions',
  authenticateToken,
  requirePermission('role.read'),
  getRolePermissions
)

/**
 * @route PUT /api/roles/:id/permissions
 * @desc 为角色分配权限
 * @access 需要 permission.assign 权限
 */
router.put('/:id/permissions',
  authenticateToken,
  requirePermission('permission.assign'),
  assignRolePermissions
)

/**
 * @route GET /api/roles/:id/users
 * @desc 获取角色的用户列表
 * @access 需要 role.read 权限
 */
router.get('/:id/users',
  authenticateToken,
  requirePermission('role.read'),
  getRoleUsers
)

export default router
