import { Router } from 'express'
import { MediaController } from '../controllers/media'
import { authenticateToken } from '../middleware/auth'
import { requirePermission, requirePermissionOrOwnership } from '../middleware/permission'

const router = Router()

/**
 * 媒体管理相关路由配置
 * 所有路由都以 /api/media 为前缀
 */

/**
 * GET /api/media
 * 获取媒体列表
 * 支持分页、筛选、搜索等查询参数
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认20）
 * - category: 媒体类别（image/video/audio/document）
 * - search: 搜索关键词
 * - uploaderId: 上传者ID
 * - isPublic: 是否公开（true/false）
 * - sortBy: 排序字段（createdAt/size/originalName）
 * - sortOrder: 排序方向（ASC/DESC）
 */
router.get('/', requirePermission('media.read'), MediaController.getMediaList)

/**
 * GET /api/media/stats
 * 获取媒体统计信息
 * 返回总数、各类别数量、总大小等统计数据
 */
router.get('/stats', requirePermission('media.read'), MediaController.getMediaStats)

/**
 * GET /api/media/:id
 * 获取单个媒体详情
 * 参数：
 * - id: 媒体ID
 */
router.get('/:id', MediaController.getMediaById)

// 需要认证的路由
/**
 * PUT /api/media/:id
 * 更新媒体信息
 * 需要认证，只有上传者可以修改
 * 参数：
 * - id: 媒体ID
 * 请求体：
 * - description: 媒体描述（可选）
 * - tags: 标签数组（可选）
 * - isPublic: 是否公开（可选）
 */
router.put('/:id', authenticateToken, requirePermissionOrOwnership('media.update', 'Media', 'uploader_id'), MediaController.updateMedia)

/**
 * DELETE /api/media/:id
 * 删除媒体文件
 * 需要认证，只有上传者可以删除
 * 参数：
 * - id: 媒体ID
 */
router.delete('/:id', authenticateToken, requirePermissionOrOwnership('media.delete', 'Media', 'uploader_id'), MediaController.deleteMedia)

export default router
