import { Router } from 'express'
import {
  getTags,
  getTagArticles,
  createTag,
  deleteTag,
  updateTag,
  batchDeleteTags,
  getTagStats,
  getPopularTags,
  addTagToArticle,
  removeTagFromArticle,
  getArticleTags
} from '../controllers/tag'
import { authenticateToken } from '../middleware/auth'
import { requirePermission } from '../middleware/permission'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     Tag:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Tag ID
 *         name:
 *           type: string
 *           description: Tag name
 *         slug:
 *           type: string
 *           description: URL-friendly tag name
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         articleCount:
 *           type: integer
 *           description: Number of published articles with this tag
 */

/**
 * @swagger
 * /api/tags:
 *   get:
 *     summary: Get all tags
 *     tags: [Tags]
 *     responses:
 *       200:
 *         description: List of tags retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 tags:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Tag'
 */
router.get('/', getTags)

/**
 * @swagger
 * /api/tags:
 *   post:
 *     summary: Create a new tag
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Tag name
 *     responses:
 *       201:
 *         description: Tag created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 tag:
 *                   $ref: '#/components/schemas/Tag'
 *       400:
 *         description: Validation error
 *       409:
 *         description: Tag already exists
 *       401:
 *         description: Unauthorized
 */
router.post('/', authenticateToken, requirePermission('tag.create'), createTag)

/**
 * @swagger
 * /api/tags/{id}:
 *   put:
 *     summary: Update a tag
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tag ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Updated tag name
 *     responses:
 *       200:
 *         description: Tag updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 tag:
 *                   $ref: '#/components/schemas/Tag'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Tag not found
 *       409:
 *         description: Tag name already exists
 *       401:
 *         description: Unauthorized
 */
router.put('/:id', authenticateToken, requirePermission('tag.update'), updateTag)

/**
 * @swagger
 * /api/tags/{id}:
 *   delete:
 *     summary: Delete a tag
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tag ID
 *     responses:
 *       200:
 *         description: Tag deleted successfully
 *       404:
 *         description: Tag not found
 *       401:
 *         description: Unauthorized
 */
router.delete('/:id', authenticateToken, requirePermission('tag.delete'), deleteTag)

/**
 * @swagger
 * /api/tags/batch-delete:
 *   post:
 *     summary: Batch delete tags
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tagIds
 *             properties:
 *               tagIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of tag IDs to delete
 *     responses:
 *       200:
 *         description: Tags deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 deletedCount:
 *                   type: integer
 *                 requestedCount:
 *                   type: integer
 *       400:
 *         description: Validation error
 *       404:
 *         description: No tags found
 *       401:
 *         description: Unauthorized
 */
router.post('/batch-delete', authenticateToken, requirePermission('tag.delete'), batchDeleteTags)

/**
 * @swagger
 * /api/tags/stats:
 *   get:
 *     summary: Get tag statistics
 *     tags: [Tags]
 *     responses:
 *       200:
 *         description: Tag statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 stats:
 *                   type: object
 *                   properties:
 *                     totalTags:
 *                       type: integer
 *                       description: Total number of tags
 *                     usedTags:
 *                       type: integer
 *                       description: Number of tags with articles
 *                     unusedTags:
 *                       type: integer
 *                       description: Number of tags without articles
 *                     averageArticlesPerTag:
 *                       type: number
 *                       description: Average number of articles per tag
 *                 tags:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Tag'
 *                       - type: object
 *                         properties:
 *                           articleCount:
 *                             type: integer
 */
router.get('/stats', getTagStats)

/**
 * @swagger
 * /api/tags/popular:
 *   get:
 *     summary: Get popular tags
 *     tags: [Tags]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of popular tags to return
 *     responses:
 *       200:
 *         description: Popular tags retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 tags:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Tag'
 *                       - type: object
 *                         properties:
 *                           articleCount:
 *                             type: integer
 */
router.get('/popular', getPopularTags)

/**
 * @swagger
 * /api/tags/{tagName}/articles:
 *   get:
 *     summary: Get articles by tag
 *     tags: [Tags]
 *     parameters:
 *       - in: path
 *         name: tagName
 *         required: true
 *         schema:
 *           type: string
 *         description: Tag name or slug
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of articles per page
 *     responses:
 *       200:
 *         description: Articles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 articles:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Article'
 *                 tag:
 *                   $ref: '#/components/schemas/Tag'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       404:
 *         description: Tag not found
 */
router.get('/:tagName/articles', getTagArticles)

/**
 * @swagger
 * /api/tags/article-association:
 *   post:
 *     summary: Add tag to article
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - articleId
 *               - tagId
 *             properties:
 *               articleId:
 *                 type: integer
 *                 description: Article ID
 *               tagId:
 *                 type: integer
 *                 description: Tag ID
 *     responses:
 *       201:
 *         description: Tag added to article successfully
 *       404:
 *         description: Article or tag not found
 *       409:
 *         description: Tag already associated with article
 *       401:
 *         description: Unauthorized
 */
router.post('/article-association', authenticateToken, requirePermission('tag.manage'), addTagToArticle)

/**
 * @swagger
 * /api/tags/article-association/{articleId}/{tagId}:
 *   delete:
 *     summary: Remove tag from article
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: articleId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Article ID
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tag ID
 *     responses:
 *       200:
 *         description: Tag removed from article successfully
 *       404:
 *         description: Article, tag, or association not found
 *       401:
 *         description: Unauthorized
 */
router.delete('/article-association/:articleId/:tagId', authenticateToken, requirePermission('tag.manage'), removeTagFromArticle)

/**
 * @swagger
 * /api/tags/article/{articleId}:
 *   get:
 *     summary: Get tags for a specific article
 *     tags: [Tags]
 *     parameters:
 *       - in: path
 *         name: articleId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Article ID
 *     responses:
 *       200:
 *         description: Article tags retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 tags:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Tag'
 *       404:
 *         description: Article not found
 */
router.get('/article/:articleId', getArticleTags)

export default router