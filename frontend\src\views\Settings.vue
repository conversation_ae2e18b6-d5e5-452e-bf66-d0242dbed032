<template>
  <div class="settings-container">
    <!-- 页面头部 -->
    <div class="settings-header">
      <h1 class="settings-title">
        <el-icon><Setting /></el-icon>
        设置
      </h1>
      <p class="settings-subtitle">管理您的个人信息、偏好设置和隐私选项</p>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 左侧导航菜单 -->
      <div class="settings-sidebar">
        <el-menu
          :default-active="activeTab"
          mode="vertical"
          @select="handleTabChange"
          class="settings-menu"
        >
          <el-menu-item index="profile">
            <el-icon><User /></el-icon>
            <span>个人信息</span>
          </el-menu-item>
          <el-menu-item index="preferences">
            <el-icon><Tools /></el-icon>
            <span>偏好设置</span>
          </el-menu-item>
          <el-menu-item index="notifications">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
            <el-badge
              v-if="notificationSummary.enabled > 0"
              :value="notificationSummary.enabled"
              class="notification-badge"
            />
          </el-menu-item>
          <el-menu-item index="privacy">
            <el-icon><Lock /></el-icon>
            <span>隐私设置</span>
          </el-menu-item>
          <el-menu-item index="security">
            <el-icon><Shield /></el-icon>
            <span>安全设置</span>
            <el-badge
              v-if="securitySummary.twoFactorEnabled"
              is-dot
              class="security-badge"
            />
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧设置面板 -->
      <div class="settings-main">
        <el-card class="settings-card" shadow="never">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="8" animated />
          </div>

          <!-- 错误状态 -->
          <el-alert
            v-if="error"
            :title="error"
            type="error"
            show-icon
            :closable="false"
            class="error-alert"
          />

          <!-- 设置内容 -->
          <div v-if="!loading && !error" class="settings-panel">
            <!-- 个人信息设置 -->
            <ProfileSettings
              v-if="activeTab === 'profile'"
              :settings="settings"
              :loading="saving"
              @update="handleUpdateProfile"
            />

            <!-- 偏好设置 -->
            <PreferencesSettings
              v-if="activeTab === 'preferences'"
              :settings="settings"
              :loading="saving"
              @update="handleUpdatePreferences"
            />

            <!-- 通知设置 -->
            <NotificationSettings
              v-if="activeTab === 'notifications'"
              :settings="settings"
              :loading="saving"
              @update="handleUpdateNotifications"
            />

            <!-- 隐私设置 -->
            <PrivacySettings
              v-if="activeTab === 'privacy'"
              :settings="settings"
              :loading="saving"
              @update="handleUpdatePrivacy"
            />

            <!-- 安全设置 -->
            <SecuritySettings
              v-if="activeTab === 'security'"
              :settings="settings"
              :loading="saving"
              @update="handleUpdateSecurity"
            />
          </div>
        </el-card>

        <!-- 操作按钮区域 -->
        <div class="settings-actions">
          <el-button
            type="danger"
            :loading="resetting"
            @click="handleResetSettings"
            plain
          >
            <el-icon><RefreshLeft /></el-icon>
            重置为默认设置
          </el-button>

          <el-button
            type="primary"
            @click="handleSaveAll"
            :loading="saving"
          >
            <el-icon><Check /></el-icon>
            保存所有设置
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  User,
  Tools,
  Bell,
  Lock,
  Shield,
  RefreshLeft,
  Check
} from '@element-plus/icons-vue'

import { useSettingsStore } from '@/stores/settings'
import type { SettingsUpdateParams } from '@/services/settings'

// 导入设置组件
import ProfileSettings from '@/components/settings/ProfileSettings.vue'
import PreferencesSettings from '@/components/settings/PreferencesSettings.vue'
import NotificationSettings from '@/components/settings/NotificationSettings.vue'
import PrivacySettings from '@/components/settings/PrivacySettings.vue'
import SecuritySettings from '@/components/settings/SecuritySettings.vue'

// ==================== 状态管理 ====================

const route = useRoute()
const router = useRouter()
const settingsStore = useSettingsStore()

// 当前激活的标签页
const activeTab = ref('profile')

// 从store获取状态
const {
  settings,
  loading,
  saving,
  resetting,
  error,
  notificationSummary,
  securitySummary
} = settingsStore

// ==================== 生命周期 ====================

onMounted(async () => {
  // 从路由参数获取初始标签页
  if (route.query.tab) {
    activeTab.value = route.query.tab as string
  }

  // 初始化设置数据
  await settingsStore.initialize()
})

// ==================== 事件处理 ====================

/**
 * 处理标签页切换
 */
const handleTabChange = (key: string) => {
  activeTab.value = key

  // 更新路由查询参数
  router.replace({
    query: { ...route.query, tab: key }
  })
}

/**
 * 处理个人信息更新
 */
const handleUpdateProfile = async (profileData: SettingsUpdateParams) => {
  try {
    await settingsStore.updateProfile(profileData)
    ElMessage.success('个人信息更新成功')
  } catch (error) {
    ElMessage.error('个人信息更新失败')
  }
}

/**
 * 处理偏好设置更新
 */
const handleUpdatePreferences = async (preferencesData: SettingsUpdateParams) => {
  try {
    await settingsStore.updatePreferences(preferencesData)
    ElMessage.success('偏好设置更新成功')
  } catch (error) {
    ElMessage.error('偏好设置更新失败')
  }
}

/**
 * 处理通知设置更新
 */
const handleUpdateNotifications = async (notificationsData: SettingsUpdateParams) => {
  try {
    await settingsStore.updateNotifications(notificationsData)
    ElMessage.success('通知设置更新成功')
  } catch (error) {
    ElMessage.error('通知设置更新失败')
  }
}

/**
 * 处理隐私设置更新
 */
const handleUpdatePrivacy = async (privacyData: SettingsUpdateParams) => {
  try {
    await settingsStore.updatePrivacy(privacyData)
    ElMessage.success('隐私设置更新成功')
  } catch (error) {
    ElMessage.error('隐私设置更新失败')
  }
}

/**
 * 处理安全设置更新
 */
const handleUpdateSecurity = async (securityData: SettingsUpdateParams) => {
  try {
    await settingsStore.updateSecurity(securityData)
    ElMessage.success('安全设置更新成功')
  } catch (error) {
    ElMessage.error('安全设置更新失败')
  }
}

/**
 * 处理重置设置
 */
const handleResetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有设置为默认值吗？此操作不可撤销。',
      '重置设置',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await settingsStore.resetSettings()
    ElMessage.success('设置已重置为默认值')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置设置失败')
    }
  }
}

/**
 * 处理保存所有设置
 */
const handleSaveAll = async () => {
  try {
    // 这里可以触发所有子组件的保存操作
    ElMessage.success('所有设置已保存')
  } catch (error) {
    ElMessage.error('保存设置失败')
  }
}
</script>

<style scoped>
.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.settings-header {
  margin-bottom: 32px;
  text-align: center;
}

.settings-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.settings-subtitle {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.settings-content {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.settings-sidebar {
  width: 240px;
  flex-shrink: 0;
}

.settings-menu {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.settings-main {
  flex: 1;
  min-width: 0;
}

.settings-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid var(--el-border-color-light);
}

.loading-container {
  padding: 24px;
}

.error-alert {
  margin-bottom: 24px;
}

.settings-panel {
  padding: 24px;
}

.settings-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.notification-badge {
  margin-left: 8px;
}

.security-badge {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-content {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
  }
  
  .settings-menu {
    display: flex;
    overflow-x: auto;
  }
  
  .settings-actions {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
