<template>
  <div class="privacy-settings">
    <div class="settings-section-header">
      <h3 class="section-title">
        <el-icon><Lock /></el-icon>
        隐私设置
      </h3>
      <p class="section-description">控制您的信息可见性和隐私保护</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="140px"
      class="privacy-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 个人资料可见性 -->
      <el-form-item label="个人资料可见性" prop="profileVisibility">
        <div class="privacy-item">
          <el-radio-group v-model="formData.profileVisibility" class="visibility-group">
            <el-radio value="public" class="visibility-option">
              <div class="option-content">
                <div class="option-header">
                  <el-icon class="option-icon public"><View /></el-icon>
                  <span class="option-title">公开</span>
                </div>
                <div class="option-desc">
                  任何人都可以查看您的个人资料
                </div>
              </div>
            </el-radio>
            <el-radio value="private" class="visibility-option">
              <div class="option-content">
                <div class="option-header">
                  <el-icon class="option-icon private"><Hide /></el-icon>
                  <span class="option-title">私有</span>
                </div>
                <div class="option-desc">
                  只有您自己可以查看完整的个人资料
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </el-form-item>

      <!-- 默认文章可见性 -->
      <el-form-item label="默认文章可见性" prop="defaultPostVisibility">
        <div class="privacy-item">
          <el-radio-group v-model="formData.defaultPostVisibility" class="visibility-group">
            <el-radio value="public" class="visibility-option">
              <div class="option-content">
                <div class="option-header">
                  <el-icon class="option-icon public"><Document /></el-icon>
                  <span class="option-title">公开</span>
                </div>
                <div class="option-desc">
                  新发布的文章默认为公开可见
                </div>
              </div>
            </el-radio>
            <el-radio value="private" class="visibility-option">
              <div class="option-content">
                <div class="option-header">
                  <el-icon class="option-icon private"><DocumentDelete /></el-icon>
                  <span class="option-title">私有</span>
                </div>
                <div class="option-desc">
                  新发布的文章默认为私有，只有您可以查看
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </el-form-item>

      <!-- 邮箱地址显示 -->
      <el-form-item label="邮箱地址显示">
        <div class="privacy-item">
          <div class="switch-item">
            <el-switch
              v-model="formData.showEmail"
              size="large"
              :active-icon="Message"
              :inactive-icon="Hide"
            />
            <div class="switch-info">
              <div class="switch-title">在个人资料中显示邮箱地址</div>
              <div class="switch-desc">
                其他用户可以在您的个人资料页面看到您的邮箱地址
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 隐私提醒 -->
      <el-form-item>
        <el-alert
          :title="privacyWarning"
          type="warning"
          show-icon
          :closable="false"
          class="privacy-warning"
        />
      </el-form-item>

      <!-- 隐私摘要 -->
      <el-form-item label="隐私摘要">
        <el-card class="privacy-summary" shadow="never">
          <div class="summary-content">
            <div class="summary-items">
              <div class="summary-item">
                <el-icon :class="['summary-icon', formData.profileVisibility === 'private' ? 'private' : 'public']">
                  <Lock v-if="formData.profileVisibility === 'private'" />
                  <View v-else />
                </el-icon>
                <span class="summary-text">
                  个人资料：{{ formData.profileVisibility === 'private' ? '私有' : '公开' }}
                </span>
              </div>
              <div class="summary-item">
                <el-icon :class="['summary-icon', formData.defaultPostVisibility === 'private' ? 'private' : 'public']">
                  <Lock v-if="formData.defaultPostVisibility === 'private'" />
                  <Document v-else />
                </el-icon>
                <span class="summary-text">
                  默认文章：{{ formData.defaultPostVisibility === 'private' ? '私有' : '公开' }}
                </span>
              </div>
              <div class="summary-item">
                <el-icon :class="['summary-icon', formData.showEmail ? 'public' : 'private']">
                  <Message v-if="formData.showEmail" />
                  <Hide v-else />
                </el-icon>
                <span class="summary-text">
                  邮箱显示：{{ formData.showEmail ? '显示' : '隐藏' }}
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            保存隐私设置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  Lock, 
  View, 
  Hide, 
  Document, 
  DocumentDelete, 
  Message, 
  Check 
} from '@element-plus/icons-vue'

import type { UserSettings, SettingsUpdateParams } from '@/services/settings'

// ==================== Props & Emits ====================

interface Props {
  settings: UserSettings | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  update: [data: SettingsUpdateParams]
}>()

// ==================== 响应式数据 ====================

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  profileVisibility: 'public' as 'public' | 'private',
  defaultPostVisibility: 'public' as 'public' | 'private',
  showEmail: false
})

// 表单验证规则
const rules: FormRules = {
  profileVisibility: [
    { required: true, message: '请选择个人资料可见性', trigger: 'change' }
  ],
  defaultPostVisibility: [
    { required: true, message: '请选择默认文章可见性', trigger: 'change' }
  ]
}

// ==================== 计算属性 ====================

// 隐私警告信息
const privacyWarning = computed(() => {
  if (formData.profileVisibility === 'private' && formData.defaultPostVisibility === 'public') {
    return '注意：您的个人资料设为私有，但默认文章可见性为公开，这可能会造成信息不一致。'
  }
  if (formData.profileVisibility === 'private' && formData.showEmail) {
    return '注意：您的个人资料设为私有，但选择显示邮箱地址，邮箱仍可能被其他用户看到。'
  }
  return '您的隐私设置看起来合理，请确认后保存。'
})

// ==================== 监听器 ====================

// 监听设置数据变化，更新表单
watch(
  () => props.settings,
  (newSettings) => {
    if (newSettings) {
      formData.profileVisibility = newSettings.profileVisibility || 'public'
      formData.defaultPostVisibility = newSettings.defaultPostVisibility || 'public'
      formData.showEmail = newSettings.showEmail ?? false
    }
  },
  { immediate: true }
)

// ==================== 方法 ====================

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 准备更新数据
    const updateData: SettingsUpdateParams = {
      profileVisibility: formData.profileVisibility,
      defaultPostVisibility: formData.defaultPostVisibility,
      showEmail: formData.showEmail
    }

    emit('update', updateData)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}

/**
 * 重置表单
 */
const handleReset = () => {
  if (props.settings) {
    formData.profileVisibility = props.settings.profileVisibility || 'public'
    formData.defaultPostVisibility = props.settings.defaultPostVisibility || 'public'
    formData.showEmail = props.settings.showEmail ?? false
  }
  
  formRef.value?.clearValidate()
  ElMessage.success('表单已重置')
}
</script>

<style scoped>
.privacy-settings {
  max-width: 600px;
}

.settings-section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.privacy-form {
  margin-top: 24px;
}

.privacy-item {
  width: 100%;
}

.visibility-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.visibility-option {
  width: 100%;
  margin: 0;
  padding: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
  transition: all 0.3s ease;
}

.visibility-option:hover {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.visibility-option.is-checked {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.option-content {
  width: 100%;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.option-icon {
  font-size: 18px;
}

.option-icon.public {
  color: var(--el-color-success);
}

.option-icon.private {
  color: var(--el-color-warning);
}

.option-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.option-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  margin-left: 26px;
}

.switch-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
}

.switch-info {
  flex: 1;
}

.switch-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.switch-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.privacy-warning {
  margin: 16px 0;
}

.privacy-summary {
  border: 1px solid var(--el-border-color-light);
  background: var(--el-bg-color-page);
}

.summary-content {
  width: 100%;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-icon {
  font-size: 16px;
}

.summary-icon.public {
  color: var(--el-color-success);
}

.summary-icon.private {
  color: var(--el-color-warning);
}

.summary-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .switch-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
