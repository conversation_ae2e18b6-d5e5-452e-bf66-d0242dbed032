# 媒体种子数据文档

## 概述

本文档描述了媒体管理系统的种子数据设计和实现。媒体种子数据为系统提供了丰富的示例媒体文件，用于演示和测试媒体管理功能。

## 种子数据文件

### 文件位置
- **种子文件**: `backend/src/database/seeders/008-media.ts`
- **执行顺序**: 第8个执行的种子文件（在用户、分类、标签、文章等基础数据之后）

### 数据结构

种子数据包含了四种主要的媒体类型：

#### 1. 图片类型 (Image)
- **hero-banner-2024.jpg**: 网站首页横幅图片 (2MB, 1920x1080)
- **blog-cover-tech.png**: 技术博客封面图片 (1.5MB, 1200x630)
- **avatar-placeholder.svg**: 默认用户头像 (8KB, 200x200)
- **gallery-photo-1.jpg**: 风景摄影作品 (3MB, 2560x1440)

#### 2. 视频类型 (Video)
- **intro-video-2024.mp4**: 产品介绍视频 (50MB, 1920x1080)
- **tutorial-basics.webm**: 基础教程视频 (30MB, 1280x720)

#### 3. 音频类型 (Audio)
- **background-music.mp3**: 背景音乐 (4MB)
- **podcast-episode-1.wav**: 技术播客第一期 (24MB)

#### 4. 文档类型 (Document)
- **user-manual-v2.pdf**: 用户操作手册 (2MB)
- **api-documentation.docx**: API接口文档 (1MB, 私有)
- **project-proposal.pptx**: 项目提案演示 (5MB, 私有)
- **data-export.xlsx**: 数据导出表格 (3MB, 私有)

## 数据特性

### 文件属性
每个媒体文件包含以下完整属性：
- **基本信息**: 文件名、原始名称、MIME类型、文件大小
- **访问信息**: URL路径、缩略图URL（适用时）
- **元数据**: 宽度、高度（图片/视频）
- **分类信息**: 媒体类型、标签数组、描述
- **权限控制**: 公开/私有状态
- **关联关系**: 上传者ID（关联到用户表）
- **时间戳**: 创建时间、更新时间

### 标签系统
每个媒体文件都配置了相关的标签，便于分类和搜索：
- **图片标签**: 横幅、设计、摄影、图标等
- **视频标签**: 介绍、教程、分辨率标识等
- **音频标签**: 音乐、播客、格式标识等
- **文档标签**: 手册、API、演示、数据等

### 权限设置
- **公开文件**: 图片、视频、音频类型默认为公开
- **私有文件**: 部分文档类型设置为私有，模拟企业内部文件

## 用户关联

媒体文件分配给不同的用户作为上传者：
- **管理员用户**: 系统级媒体文件（横幅、默认头像、产品视频等）
- **普通用户**: 个人上传的内容（博客封面、摄影作品、播客等）

## 使用场景

### 1. 功能演示
- 媒体库浏览和搜索
- 不同类型文件的预览
- 标签过滤和分类
- 权限控制展示

### 2. 测试数据
- 上传功能测试
- 文件管理操作测试
- API接口测试
- 前端组件测试

### 3. 开发支持
- 前端组件开发时的数据支持
- UI/UX设计参考
- 性能测试基准数据

## 执行方式

### 自动执行
```bash
# 执行所有种子数据（包括媒体数据）
npm run db:seed

# 或者完整的数据库初始化
npm run db:init
```

### 单独执行
```bash
# 仅执行媒体种子数据
npm run db:seed:media
```

### 重置执行
```bash
# 强制重新执行所有种子数据
npm run db:seed:force
```

## 注意事项

### 文件路径
- 种子数据中的文件路径是示例路径
- 实际部署时需要确保对应的文件存在
- 可以使用占位符图片/文件进行演示

### 存储空间
- 总计约130MB的示例文件数据
- 生产环境可根据需要调整文件大小
- 建议使用压缩格式减少存储占用

### 数据依赖
- 依赖用户表中的用户数据
- 执行前确保用户种子数据已创建
- 遵循种子文件的执行顺序

## 扩展建议

### 添加更多类型
- 可以添加更多的媒体文件类型
- 支持更多的文件格式
- 增加不同分辨率的示例

### 本地化支持
- 添加多语言文件名示例
- 支持不同地区的媒体内容
- 考虑文化差异的媒体选择

### 业务场景
- 根据具体业务需求调整媒体类型
- 添加特定行业的媒体示例
- 模拟真实的使用场景

这套媒体种子数据为系统提供了完整的演示环境，支持各种媒体管理功能的测试和展示。
