import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { postService } from '@/services/post'
import type { Post, PostParams, PostCreateRequest, PostUpdateRequest, PostStats } from '@/services/types/post'

/**
 * 说说状态管理仓库
 * 使用Pinia定义的说说相关状态管理，包含说说列表、当前说说、分页信息、过滤条件等
 */
export const usePostStore = defineStore('post', () => {
  const posts = ref<Post[]>([])
  const currentPost = ref<Post | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页信息
  const pagination = ref({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  })

  // 过滤条件
  const filters = ref({
    search: '',
    visibility: '' as '' | 'public' | 'private',
    authorId: null as number | null
  })

  // 统计信息
  const stats = ref<PostStats>({
    total: 0,
    public: 0,
    private: 0,
    todayCount: 0
  })

  /**
   * 获取说说列表
   * @param page - 页码，默认为1
   * @param limit - 每页条数，默认为10
   * @param params - 查询参数，包含可见性、作者、搜索关键词等
   * @returns 返回包含说说列表和分页信息的响应数据
   */
  const fetchPosts = async (page = 1, limit = 10, params?: PostParams) => {
    loading.value = true
    error.value = null

    try {
      const response = await postService.getPosts({
        page,
        limit,
        visibility: params?.visibility || filters.value.visibility || undefined,
        authorId: params?.authorId || filters.value.authorId || undefined,
        search: params?.search || filters.value.search || undefined,
        sort: params?.sort,
        order: params?.order
      })
      posts.value = response.posts

      // 将后端分页格式转换为前端格式
      pagination.value = {
        currentPage: response.pagination.page,
        totalPages: response.pagination.totalPages,
        totalItems: response.pagination.total,
        itemsPerPage: response.pagination.limit,
        hasNextPage: response.pagination.page < response.pagination.totalPages,
        hasPrevPage: response.pagination.page > 1
      }

      return response
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取单个说说详情
   * @param id - 说说ID
   * @returns 返回说说详情数据
   */
  const fetchPost = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const post = await postService.getPost(id)
      currentPost.value = post
      return post
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建新说说
   * @param postData - 说说数据
   * @returns 返回创建的说说对象
   */
  const createPost = async (postData: PostCreateRequest) => {
    loading.value = true
    error.value = null

    try {
      const newPost = await postService.createPost(postData)
      // 将新说说添加到列表开头
      posts.value.unshift(newPost)
      // 更新统计信息
      stats.value.total++
      if (newPost.visibility === 'public') {
        stats.value.public++
      } else {
        stats.value.private++
      }
      return newPost
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新说说
   * @param id - 说说ID
   * @param postData - 更新的说说数据
   * @returns 返回更新后的说说对象
   */
  const updatePost = async (id: number, postData: PostUpdateRequest) => {
    loading.value = true
    error.value = null

    try {
      const updatedPost = await postService.updatePost(id, postData)
      
      // 更新列表中的说说
      const index = posts.value.findIndex(post => post.id === id)
      if (index !== -1) {
        posts.value[index] = updatedPost
      }
      
      // 更新当前说说
      if (currentPost.value?.id === id) {
        currentPost.value = updatedPost
      }
      
      return updatedPost
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除说说
   * @param id - 说说ID
   */
  const deletePost = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      await postService.deletePost(id)
      
      // 从列表中移除说说
      const index = posts.value.findIndex(post => post.id === id)
      if (index !== -1) {
        const deletedPost = posts.value[index]
        posts.value.splice(index, 1)
        
        // 更新统计信息
        stats.value.total--
        if (deletedPost.visibility === 'public') {
          stats.value.public--
        } else {
          stats.value.private--
        }
      }
      
      // 清除当前说说
      if (currentPost.value?.id === id) {
        currentPost.value = null
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换说说点赞状态
   * @param id - 说说ID
   */
  const toggleLike = async (id: number) => {
    try {
      const result = await postService.toggleLike(id)
      
      // 更新列表中的说说点赞状态
      const index = posts.value.findIndex(post => post.id === id)
      if (index !== -1) {
        posts.value[index].isLiked = result.action === 'liked'
        posts.value[index].likeCount = result.likeCount
      }
      
      // 更新当前说说点赞状态
      if (currentPost.value?.id === id) {
        currentPost.value.isLiked = result.action === 'liked'
        currentPost.value.likeCount = result.likeCount
      }
      
      return result
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 获取说说统计信息
   */
  const fetchStats = async () => {
    try {
      const statsData = await postService.getPostStats()
      stats.value = statsData
      return statsData
    } catch (err: any) {
      console.error('获取说说统计失败:', err)
    }
  }

  /**
   * 批量删除说说
   * @param ids - 说说ID数组
   */
  const batchDeletePosts = async (ids: number[]) => {
    loading.value = true
    error.value = null

    try {
      await postService.batchDeletePosts(ids)
      
      // 从列表中移除删除的说说
      const deletedPosts = posts.value.filter(post => ids.includes(post.id))
      posts.value = posts.value.filter(post => !ids.includes(post.id))
      
      // 更新统计信息
      deletedPosts.forEach(post => {
        stats.value.total--
        if (post.visibility === 'public') {
          stats.value.public--
        } else {
          stats.value.private--
        }
      })
      
      // 清除当前说说（如果被删除）
      if (currentPost.value && ids.includes(currentPost.value.id)) {
        currentPost.value = null
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置过滤条件
   * @param newFilters - 新的过滤条件
   */
  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    Object.assign(filters.value, newFilters)
  }

  /**
   * 清除过滤条件
   */
  const clearFilters = () => {
    filters.value = {
      search: '',
      visibility: '',
      authorId: null
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    posts.value = []
    currentPost.value = null
    loading.value = false
    error.value = null
    pagination.value = {
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      itemsPerPage: 10,
      hasNextPage: false,
      hasPrevPage: false
    }
    clearFilters()
  }

  // 计算属性
  const publicPosts = computed(() => posts.value.filter(post => post.visibility === 'public'))
  const privatePosts = computed(() => posts.value.filter(post => post.visibility === 'private'))
  const hasNextPage = computed(() => pagination.value.hasNextPage)
  const hasPrevPage = computed(() => pagination.value.hasPrevPage)

  return {
    // 状态
    posts,
    currentPost,
    loading,
    error,
    pagination,
    filters,
    stats,
    
    // 计算属性
    publicPosts,
    privatePosts,
    hasNextPage,
    hasPrevPage,
    
    // 方法
    fetchPosts,
    fetchPost,
    createPost,
    updatePost,
    deletePost,
    toggleLike,
    fetchStats,
    batchDeletePosts,
    setFilters,
    clearFilters,
    resetState
  }
})
