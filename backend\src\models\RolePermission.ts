import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 角色权限关联模型的属性接口，定义了角色权限关联对象的基本字段结构
 */
export interface RolePermissionAttributes {
  id: number
  roleId: number
  permissionId: number
  assignedBy?: number
  assignedAt: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * 角色权限关联创建时的属性接口，继承自 RolePermissionAttributes，但允许部分字段为空
 */
export interface RolePermissionCreationAttributes extends Optional<RolePermissionAttributes, 'id' | 'assignedAt' | 'createdAt' | 'updatedAt'> {}

/**
 * 角色权限关联模型类，用于与数据库中的 role_permissions 表进行交互
 * 实现了 RolePermissionAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class RolePermission extends Model<RolePermissionAttributes, RolePermissionCreationAttributes> implements RolePermissionAttributes {
  public id!: number
  public roleId!: number
  public permissionId!: number
  public assignedBy?: number
  public assignedAt!: Date
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly role?: any
  public readonly permission?: any
  public readonly assigner?: any

  /**
   * 根据角色ID查找角色的所有权限关联
   * @param roleId - 角色ID
   * @returns 返回角色权限关联列表
   */
  public static async findByRoleId(roleId: number): Promise<RolePermission[]> {
    return this.findAll({ 
      where: { roleId },
      include: ['permission']
    })
  }

  /**
   * 根据权限ID查找拥有该权限的所有角色关联
   * @param permissionId - 权限ID
   * @returns 返回角色权限关联列表
   */
  public static async findByPermissionId(permissionId: number): Promise<RolePermission[]> {
    return this.findAll({ 
      where: { permissionId },
      include: ['role']
    })
  }

  /**
   * 检查角色是否拥有指定权限
   * @param roleId - 角色ID
   * @param permissionId - 权限ID
   * @returns 返回是否拥有权限
   */
  public static async hasPermission(roleId: number, permissionId: number): Promise<boolean> {
    const count = await this.count({ where: { roleId, permissionId } })
    return count > 0
  }

  /**
   * 检查角色是否拥有指定名称的权限
   * @param roleId - 角色ID
   * @param permissionName - 权限名称
   * @returns 返回是否拥有权限
   */
  public static async hasPermissionByName(roleId: number, permissionName: string): Promise<boolean> {
    const Permission = sequelize.models.Permission
    const count = await this.count({
      where: { roleId },
      include: [{
        model: Permission,
        as: 'permission',
        where: { name: permissionName }
      }]
    })
    return count > 0
  }

  /**
   * 为角色分配权限
   * @param roleId - 角色ID
   * @param permissionId - 权限ID
   * @param assignedBy - 分配者ID（可选）
   * @returns 返回创建的角色权限关联
   */
  public static async assignPermission(roleId: number, permissionId: number, assignedBy?: number): Promise<RolePermission> {
    // 检查是否已经存在该关联
    const existing = await this.findOne({ where: { roleId, permissionId } })
    if (existing) {
      return existing
    }

    return this.create({
      roleId,
      permissionId,
      assignedBy,
      assignedAt: new Date()
    })
  }

  /**
   * 移除角色的权限
   * @param roleId - 角色ID
   * @param permissionId - 权限ID
   * @returns 返回删除的记录数
   */
  public static async removePermission(roleId: number, permissionId: number): Promise<number> {
    return this.destroy({ where: { roleId, permissionId } })
  }

  /**
   * 批量为角色分配权限
   * @param roleId - 角色ID
   * @param permissionIds - 权限ID数组
   * @param assignedBy - 分配者ID（可选）
   * @returns 返回操作结果
   */
  public static async assignPermissions(roleId: number, permissionIds: number[], assignedBy?: number): Promise<void> {
    // 删除角色现有的所有权限
    await this.destroy({ where: { roleId } })

    // 分配新权限
    if (permissionIds.length > 0) {
      const rolePermissions = permissionIds.map(permissionId => ({
        roleId,
        permissionId,
        assignedBy,
        assignedAt: new Date()
      }))
      await this.bulkCreate(rolePermissions)
    }
  }

  /**
   * 获取角色的所有权限
   * @param roleId - 角色ID
   * @returns 返回权限列表
   */
  public static async getRolePermissions(roleId: number): Promise<any[]> {
    const Permission = sequelize.models.Permission
    const rolePermissions = await this.findAll({
      where: { roleId },
      include: [{
        model: Permission,
        as: 'permission',
        where: { isActive: true }
      }]
    })
    
    return rolePermissions.map(rp => rp.permission)
  }

  /**
   * 获取权限的所有角色
   * @param permissionId - 权限ID
   * @returns 返回角色列表
   */
  public static async getPermissionRoles(permissionId: number): Promise<any[]> {
    const Role = sequelize.models.Role
    const rolePermissions = await this.findAll({
      where: { permissionId },
      include: [{
        model: Role,
        as: 'role',
        where: { isActive: true }
      }]
    })
    
    return rolePermissions.map(rp => rp.role)
  }

  /**
   * 获取权限的角色数量
   * @param permissionId - 权限ID
   * @returns 返回角色数量
   */
  public static async getPermissionRoleCount(permissionId: number): Promise<number> {
    return this.count({ where: { permissionId } })
  }

  /**
   * 获取角色的权限数量
   * @param roleId - 角色ID
   * @returns 返回权限数量
   */
  public static async getRolePermissionCount(roleId: number): Promise<number> {
    return this.count({ where: { roleId } })
  }

  /**
   * 批量删除角色的权限
   * @param roleId - 角色ID
   * @param permissionIds - 权限ID数组
   * @returns 返回删除的记录数
   */
  public static async removePermissions(roleId: number, permissionIds: number[]): Promise<number> {
    return this.destroy({ 
      where: { 
        roleId,
        permissionId: permissionIds
      } 
    })
  }

  /**
   * 批量删除权限的角色关联
   * @param permissionId - 权限ID
   * @param roleIds - 角色ID数组
   * @returns 返回删除的记录数
   */
  public static async removeRoles(permissionId: number, roleIds: number[]): Promise<number> {
    return this.destroy({ 
      where: { 
        permissionId,
        roleId: roleIds
      } 
    })
  }

  /**
   * 复制角色的权限到另一个角色
   * @param sourceRoleId - 源角色ID
   * @param targetRoleId - 目标角色ID
   * @param assignedBy - 分配者ID（可选）
   * @returns 返回操作结果
   */
  public static async copyRolePermissions(sourceRoleId: number, targetRoleId: number, assignedBy?: number): Promise<void> {
    const sourcePermissions = await this.findAll({
      where: { roleId: sourceRoleId },
      attributes: ['permissionId']
    })

    const permissionIds = sourcePermissions.map(rp => rp.permissionId)
    await this.assignPermissions(targetRoleId, permissionIds, assignedBy)
  }

  /**
   * 序列化角色权限关联信息，用于API响应
   * @returns 返回序列化后的角色权限关联信息
   */
  public toJSON(): Omit<RolePermissionAttributes, never> {
    const values = { ...this.get() }
    return values
  }
}

/**
 * 初始化 RolePermission 模型，配置其字段、验证规则和索引
 */
RolePermission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    roleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'role_id',
      references: {
        model: 'roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: '角色ID'
    },
    permissionId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'permission_id',
      references: {
        model: 'permissions',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: '权限ID'
    },
    assignedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'assigned_by',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: '分配者ID'
    },
    assignedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'assigned_at',
      comment: '分配时间'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'RolePermission',
    tableName: 'role_permissions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['role_id', 'permission_id']
      },
      {
        fields: ['role_id']
      },
      {
        fields: ['permission_id']
      },
      {
        fields: ['assigned_by']
      },
      {
        fields: ['assigned_at']
      }
    ]
  }
)
