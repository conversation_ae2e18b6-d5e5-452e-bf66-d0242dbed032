<template>
  <div class="admin-comments-view">
    <!-- 页面头部 -->
    <header class="admin-header">
      <h1>评论管理</h1>
      <div class="user-info">
        <span v-if="authStore.user">欢迎, {{ authStore.user.username }}</span>
        <button @click="handleLogout" class="logout-button">退出登录</button>
      </div>
    </header>

    <!-- 导航栏 -->
    <nav class="admin-nav">
      <router-link to="/admin" class="nav-link">仪表板</router-link>
      <router-link to="/admin/articles" class="nav-link">文章管理</router-link>
      <router-link to="/admin/posts" class="nav-link">说说管理</router-link>
      <router-link to="/admin/media" class="nav-link">媒体管理</router-link>
      <router-link to="/admin/categories" class="nav-link">分类管理</router-link>
      <router-link to="/admin/tags" class="nav-link">标签管理</router-link>
      <router-link to="/admin/comments" class="nav-link">评论管理</router-link>
      <router-link to="/admin/notifications" class="nav-link">通知中心</router-link>
      <router-link to="/admin/roles" class="nav-link">角色管理</router-link>
      <router-link to="/admin/user-roles" class="nav-link">用户角色</router-link>
      <router-link to="/settings" class="nav-link">设置</router-link>
    </nav>

    <!-- 主内容区域 -->
    <main class="admin-content">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><ChatLineRound /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ commentStats?.total || 0 }}</div>
              <div class="stat-label">总评论数</div>
            </div>
          </div>
          <div class="stat-card pending" @click="filterByStatus('pending')">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ commentStats?.pending || 0 }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </div>
          <div class="stat-card approved" @click="filterByStatus('approved')">
            <div class="stat-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ commentStats?.approved || 0 }}</div>
              <div class="stat-label">已批准</div>
            </div>
          </div>
          <div class="stat-card rejected" @click="filterByStatus('rejected')">
            <div class="stat-icon">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ commentStats?.rejected || 0 }}</div>
              <div class="stat-label">已拒绝</div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <el-button
            @click="refreshData"
            :icon="Refresh"
            :loading="loading"
            size="default"
          >
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filters-section">
        <div class="filters-row">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索评论内容、作者、文章标题..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              @clear="handleClearSearch"
              class="search-input"
            />
          </div>

          <!-- 筛选器 -->
          <div class="filter-controls">
            <el-select
              v-model="statusFilter"
              placeholder="状态"
              clearable
              @change="handleFilterChange"
              class="filter-select"
            >
              <el-option label="全部状态" value="" />
              <el-option label="待审核" value="pending" />
              <el-option label="已批准" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>

            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilterChange"
              class="date-picker"
              size="default"
            />
          </div>
        </div>

        <!-- 批量操作栏 -->
        <div v-if="selectedComments.length > 0" class="batch-actions">
          <div class="batch-info">
            <span>已选择 {{ selectedComments.length }} 条评论</span>
          </div>
          <div class="batch-buttons">
            <el-button
              @click="batchApprove"
              :icon="Check"
              type="success"
              size="small"
            >
              批量批准
            </el-button>
            <el-button
              @click="batchReject"
              :icon="Close"
              type="warning"
              size="small"
            >
              批量拒绝
            </el-button>
            <el-button
              @click="batchDelete"
              :icon="Delete"
              type="danger"
              size="small"
            >
              批量删除
            </el-button>
            <el-button
              @click="clearSelection"
              :icon="RefreshLeft"
              size="small"
            >
              取消选择
            </el-button>
          </div>
        </div>
      </div>

      <!-- 评论列表表格 -->
      <div class="table-section">
        <el-table
          ref="commentTable"
          v-loading="loading"
          :data="comments"
          @selection-change="handleSelectionChange"
          class="comments-table"
          stripe
          border
        >
          <!-- 选择列 -->
          <el-table-column type="selection" width="55" />

          <!-- 评论内容列 -->
          <el-table-column prop="content" label="评论内容" min-width="300">
            <template #default="{ row }">
              <div class="content-cell">
                <div class="comment-content" @click="showCommentDetail(row)">
                  {{ truncateContent(row.content, 100) }}
                </div>
                <div class="comment-meta">
                  <span class="comment-id">ID: {{ row.id }}</span>
                  <span v-if="row.parentId" class="reply-indicator">回复</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 作者列 -->
          <el-table-column prop="author" label="作者" width="120">
            <template #default="{ row }">
              <div class="author-cell">
                <div class="author-name">{{ row.author?.username || '未知用户' }}</div>
                <div class="author-id">ID: {{ row.authorId }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 文章列 -->
          <el-table-column prop="article" label="关联文章" width="200">
            <template #default="{ row }">
              <div class="article-cell">
                <router-link
                  v-if="row.article"
                  :to="`/article/${row.article.id}`"
                  class="article-link"
                  target="_blank"
                >
                  {{ truncateContent(row.article.title, 50) }}
                </router-link>
                <span v-else class="no-article">文章已删除</span>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 时间列 -->
          <el-table-column prop="createdAt" label="创建时间" width="160">
            <template #default="{ row }">
              <div class="time-cell">
                <div class="create-time">{{ formatDate(row.createdAt) }}</div>
                <div class="relative-time">{{ getRelativeTime(row.createdAt) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  v-if="row.status === 'pending'"
                  @click="approveComment(row)"
                  :icon="Check"
                  type="success"
                  size="small"
                  title="批准"
                />
                <el-button
                  v-if="row.status !== 'rejected'"
                  @click="rejectComment(row)"
                  :icon="Close"
                  type="warning"
                  size="small"
                  title="拒绝"
                />
                <el-button
                  @click="showCommentDetail(row)"
                  :icon="View"
                  type="primary"
                  size="small"
                  title="查看详情"
                />
                <el-button
                  @click="deleteComment(row)"
                  :icon="Delete"
                  type="danger"
                  size="small"
                  title="删除"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </main>

    <!-- 评论详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="评论详情"
      width="800px"
      :before-close="handleDetailClose"
    >
      <div v-if="currentComment" class="comment-detail">
        <!-- 评论基本信息 -->
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="评论ID">{{ currentComment.id }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(currentComment.status)">
                {{ getStatusText(currentComment.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="作者">{{ currentComment.author?.username || '未知用户' }}</el-descriptions-item>
            <el-descriptions-item label="作者ID">{{ currentComment.authorId }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDate(currentComment.createdAt) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatDate(currentComment.updatedAt) }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 评论内容 -->
        <div class="detail-section">
          <h4>评论内容</h4>
          <div class="comment-content-full">
            {{ currentComment.content }}
          </div>
        </div>

        <!-- 关联文章 -->
        <div v-if="currentComment.article" class="detail-section">
          <h4>关联文章</h4>
          <div class="article-info">
            <p><strong>标题：</strong>{{ currentComment.article.title }}</p>
            <p><strong>文章ID：</strong>{{ currentComment.article.id }}</p>
            <el-button
              @click="goToArticle(currentComment.article.id)"
              type="primary"
              size="small"
            >
              查看文章
            </el-button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <el-button
            v-if="currentComment.status === 'pending'"
            @click="approveComment(currentComment)"
            type="success"
            :icon="Check"
          >
            批准评论
          </el-button>
          <el-button
            v-if="currentComment.status !== 'rejected'"
            @click="rejectComment(currentComment)"
            type="warning"
            :icon="Close"
          >
            拒绝评论
          </el-button>
          <el-button
            @click="deleteComment(currentComment)"
            type="danger"
            :icon="Delete"
          >
            删除评论
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useCommentStore } from '@/stores/comment'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatLineRound,
  Clock,
  Check,
  Close,
  Search,
  Refresh,
  Delete,
  View,
  RefreshLeft
} from '@element-plus/icons-vue'
import type { Comment } from '@/services/types/comment'

const router = useRouter()
const authStore = useAuthStore()
const commentStore = useCommentStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const selectedComments = ref<Comment[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const detailDialogVisible = ref(false)
const currentComment = ref<Comment | null>(null)

// 计算属性
const comments = computed(() => commentStore.comments)
const total = computed(() => commentStore.pagination.total)
const commentStats = computed(() => commentStore.stats)

// 搜索防抖
let searchTimeout: NodeJS.Timeout | null = null

// 方法
const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

const refreshData = async () => {
  await Promise.all([
    fetchComments(),
    fetchStats()
  ])
}

const fetchComments = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      limit: pageSize.value
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    if (dateRange.value) {
      params.startDate = dateRange.value[0].toISOString()
      params.endDate = dateRange.value[1].toISOString()
    }

    await commentStore.fetchComments(params)
  } catch (error) {
    ElMessage.error('获取评论列表失败')
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    await commentStore.fetchCommentStats()
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    fetchComments()
  }, 500)
}

const handleClearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
  fetchComments()
}

const handleFilterChange = () => {
  currentPage.value = 1
  fetchComments()
}

const filterByStatus = (status: string) => {
  statusFilter.value = status
  handleFilterChange()
}

const handleSelectionChange = (selection: Comment[]) => {
  selectedComments.value = selection
}

const clearSelection = () => {
  selectedComments.value = []
  // 清除表格选择
  const table = ref('commentTable')
  if (table.value) {
    (table.value as any).clearSelection()
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchComments()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchComments()
}

// 评论操作方法
const approveComment = async (comment: Comment) => {
  try {
    await commentStore.updateCommentStatus(comment.id, 'approved')
    ElMessage.success('评论已批准')
    await refreshData()
    if (currentComment.value?.id === comment.id) {
      currentComment.value.status = 'approved'
    }
  } catch (error) {
    ElMessage.error('批准评论失败')
  }
}

const rejectComment = async (comment: Comment) => {
  try {
    await commentStore.updateCommentStatus(comment.id, 'rejected')
    ElMessage.success('评论已拒绝')
    await refreshData()
    if (currentComment.value?.id === comment.id) {
      currentComment.value.status = 'rejected'
    }
  } catch (error) {
    ElMessage.error('拒绝评论失败')
  }
}

const deleteComment = async (comment: Comment) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条评论吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await commentStore.deleteComment(comment.id)
    ElMessage.success('评论已删除')
    await refreshData()

    if (currentComment.value?.id === comment.id) {
      detailDialogVisible.value = false
      currentComment.value = null
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除评论失败')
    }
  }
}

// 批量操作方法
const batchApprove = async () => {
  try {
    const ids = selectedComments.value.map(comment => comment.id)
    await commentStore.batchUpdateCommentStatus(ids, 'approved')
    ElMessage.success(`已批准 ${ids.length} 条评论`)
    clearSelection()
    await refreshData()
  } catch (error) {
    ElMessage.error('批量批准失败')
  }
}

const batchReject = async () => {
  try {
    const ids = selectedComments.value.map(comment => comment.id)
    await commentStore.batchUpdateCommentStatus(ids, 'rejected')
    ElMessage.success(`已拒绝 ${ids.length} 条评论`)
    clearSelection()
    await refreshData()
  } catch (error) {
    ElMessage.error('批量拒绝失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedComments.value.length} 条评论吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedComments.value.map(comment => comment.id)
    await commentStore.batchDeleteComments(ids)
    ElMessage.success(`已删除 ${ids.length} 条评论`)
    clearSelection()
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 详情弹窗方法
const showCommentDetail = (comment: Comment) => {
  currentComment.value = comment
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentComment.value = null
}

const goToArticle = (articleId: number) => {
  window.open(`/article/${articleId}`, '_blank')
}

// 工具方法
const truncateContent = (content: string, maxLength: number): string => {
  if (content.length <= maxLength) return content
  return content.substring(0, maxLength) + '...'
}

const getStatusTagType = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'pending':
      return '待审核'
    case 'approved':
      return '已批准'
    case 'rejected':
      return '已拒绝'
    default:
      return '未知'
  }
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getRelativeTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 生命周期
onMounted(async () => {
  // 检查认证状态
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 验证认证状态是否仍然有效
  const isValid = await authStore.checkAuth()
  if (!isValid) {
    router.push('/login')
    return
  }

  // 加载数据
  await refreshData()
})

// 监听筛选条件变化
watch([statusFilter, dateRange], () => {
  handleFilterChange()
})
</script>

<style scoped>
.admin-comments-view {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.admin-header {
  background: var(--el-bg-color);
  padding: 1rem 2rem;
  box-shadow: var(--el-box-shadow-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--el-border-color-light);
}

.admin-header h1 {
  color: var(--el-text-color-primary);
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info span {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.logout-button {
  padding: 8px 16px;
  background-color: var(--el-color-danger);
  color: white;
  border: none;
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.logout-button:hover {
  background-color: var(--el-color-danger-dark-2);
}

.admin-nav {
  background: var(--el-bg-color);
  padding: 0 2rem;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  gap: 2rem;
}

.nav-link {
  padding: 1rem 0;
  color: var(--el-text-color-regular);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--el-color-primary);
  border-bottom-color: var(--el-color-primary);
}

.admin-content {
  padding: 2rem;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  flex: 1;
}

.stat-card {
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  padding: 1.5rem;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: var(--el-box-shadow);
  transform: translateY(-2px);
}

.stat-card.pending {
  border-left: 4px solid var(--el-color-warning);
}

.stat-card.approved {
  border-left: 4px solid var(--el-color-success);
}

.stat-card.rejected {
  border-left: 4px solid var(--el-color-danger);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-card.pending .stat-icon {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.stat-card.approved .stat-icon {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.stat-card.rejected .stat-icon {
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.quick-actions {
  display: flex;
  gap: 1rem;
}

/* 筛选区域样式 */
.filters-section {
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
}

.filters-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
}

.filter-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  width: 120px;
}

.date-picker {
  width: 240px;
}

/* 批量操作栏样式 */
.batch-actions {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--el-color-primary-light-9);
  border-radius: var(--el-border-radius-base);
  border: 1px solid var(--el-color-primary-light-7);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  color: var(--el-color-primary);
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 0.5rem;
}

/* 表格区域样式 */
.table-section {
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  padding: 1.5rem;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
}

.comments-table {
  width: 100%;
}

.content-cell {
  max-width: 300px;
}

.comment-content {
  cursor: pointer;
  color: var(--el-text-color-primary);
  line-height: 1.5;
  margin-bottom: 4px;
  word-break: break-word;
}

.comment-content:hover {
  color: var(--el-color-primary);
}

.comment-meta {
  display: flex;
  gap: 1rem;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.comment-id {
  color: var(--el-text-color-disabled);
}

.reply-indicator {
  color: var(--el-color-warning);
  font-weight: 500;
}

.author-cell {
  text-align: center;
}

.author-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.author-id {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.article-cell {
  max-width: 200px;
}

.article-link {
  color: var(--el-color-primary);
  text-decoration: none;
  word-break: break-word;
}

.article-link:hover {
  text-decoration: underline;
}

.no-article {
  color: var(--el-text-color-disabled);
  font-style: italic;
}

.time-cell {
  text-align: center;
}

.create-time {
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.relative-time {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
}

/* 分页样式 */
.pagination-section {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

/* 详情弹窗样式 */
.comment-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.comment-content-full {
  background: var(--el-fill-color-light);
  padding: 1rem;
  border-radius: var(--el-border-radius-base);
  border: 1px solid var(--el-border-color-light);
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.article-info {
  background: var(--el-fill-color-light);
  padding: 1rem;
  border-radius: var(--el-border-radius-base);
  border: 1px solid var(--el-border-color-light);
}

.article-info p {
  margin: 0 0 0.5rem 0;
  color: var(--el-text-color-primary);
}

.detail-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .admin-content {
    padding: 1rem;
  }

  .stats-section {
    flex-direction: column;
    gap: 1rem;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-number {
    font-size: 20px;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .filter-controls {
    justify-content: space-between;
  }

  .filter-select,
  .date-picker {
    width: auto;
    flex: 1;
  }

  .batch-actions {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .batch-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .admin-nav {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .nav-link {
    padding: 0.5rem 0;
  }
}

/* 主题适配 */
[data-theme="dark"] .stat-card {
  background: var(--el-bg-color-overlay);
}

[data-theme="dark"] .comment-content-full,
[data-theme="dark"] .article-info {
  background: var(--el-bg-color-overlay);
}
</style>
