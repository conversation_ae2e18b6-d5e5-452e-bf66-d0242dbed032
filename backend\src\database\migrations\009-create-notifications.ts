/**
 * 通知系统数据库迁移
 * 
 * 创建通知表和通知偏好设置表
 * 包含完整的索引和外键约束
 */

import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('🔔 开始创建通知系统表结构...')

  // 创建通知表
  await queryInterface.createTable('notifications', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
      allowNull: false,
      comment: '通知类型：互动、内容、系统、营销'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '通知标题'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '通知内容详情'
    },
    priority: {
      type: DataTypes.ENUM('high', 'medium', 'low'),
      allowNull: false,
      defaultValue: 'medium',
      comment: '通知优先级'
    },
    recipient_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '接收者用户ID',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    sender_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '发送者用户ID（系统通知可为空）',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    related_type: {
      type: DataTypes.ENUM('article', 'post', 'comment', 'user', 'system'),
      allowNull: true,
      comment: '关联对象类型'
    },
    related_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '关联对象ID'
    },
    action_url: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '操作链接URL'
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否已读'
    },
    read_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '阅读时间'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  })

  // 创建通知偏好设置表
  await queryInterface.createTable('notification_preferences', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    notification_type: {
      type: DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
      allowNull: false,
      comment: '通知类型'
    },
    channel: {
      type: DataTypes.ENUM('in_app', 'email', 'push'),
      allowNull: false,
      defaultValue: 'in_app',
      comment: '通知渠道：站内、邮件、推送'
    },
    is_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否启用该类型通知'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  })

  // 创建通知表的索引
  console.log('📊 创建通知表索引...')

  // 接收者和创建时间的复合索引（用于获取用户通知列表）
  await queryInterface.addIndex('notifications', ['recipient_id', 'created_at'], {
    name: 'idx_recipient_created'
  })

  // 接收者和已读状态的复合索引（用于获取未读通知）
  await queryInterface.addIndex('notifications', ['recipient_id', 'is_read'], {
    name: 'idx_recipient_unread'
  })

  // 通知类型和优先级的复合索引（用于按类型和优先级查询）
  await queryInterface.addIndex('notifications', ['type', 'priority'], {
    name: 'idx_type_priority'
  })

  // 关联对象的复合索引（用于查找特定对象的相关通知）
  await queryInterface.addIndex('notifications', ['related_type', 'related_id'], {
    name: 'idx_related'
  })

  // 创建时间索引（用于清理过期通知）
  await queryInterface.addIndex('notifications', ['created_at'], {
    name: 'idx_created_at'
  })

  // 创建通知偏好设置表的索引
  console.log('⚙️ 创建通知偏好设置表索引...')

  // 用户、通知类型、渠道的唯一复合索引
  await queryInterface.addIndex('notification_preferences', ['user_id', 'notification_type', 'channel'], {
    name: 'unique_user_type_channel',
    unique: true
  })

  // 用户和启用状态的复合索引
  await queryInterface.addIndex('notification_preferences', ['user_id', 'is_enabled'], {
    name: 'idx_user_enabled'
  })

  // 通知类型、渠道、启用状态的复合索引（用于查找启用特定通知的用户）
  await queryInterface.addIndex('notification_preferences', ['notification_type', 'channel', 'is_enabled'], {
    name: 'idx_type_channel_enabled'
  })

  console.log('✅ 通知系统表结构创建完成')
  console.log('📋 已创建表：')
  console.log('   - notifications (通知表)')
  console.log('   - notification_preferences (通知偏好设置表)')
  console.log('🔍 已创建索引：')
  console.log('   - 通知表：5个索引')
  console.log('   - 偏好设置表：3个索引')
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('🗑️ 开始删除通知系统表结构...')

  // 删除通知偏好设置表（先删除依赖表）
  await queryInterface.dropTable('notification_preferences')
  console.log('✅ 已删除 notification_preferences 表')

  // 删除通知表
  await queryInterface.dropTable('notifications')
  console.log('✅ 已删除 notifications 表')

  console.log('🔔 通知系统表结构删除完成')
}
