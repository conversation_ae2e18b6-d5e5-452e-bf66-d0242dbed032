import { Router } from 'express'
import { UploadController } from '../controllers/upload'
import { authenticateToken } from '../middleware/auth'
import { requirePermission } from '../middleware/permission'

const router = Router()

/**
 * 图片上传相关路由配置
 * 所有路由都以 /api/upload 为前缀
 */

/**
 * GET /api/upload/config
 * 获取上传配置信息
 * 返回文件大小限制、支持的文件类型等信息
 */
router.get('/config', UploadController.getUploadConfig)

/**
 * GET /api/upload/image/:filename
 * 获取图片信息
 * 参数：
 * - filename: 图片文件名
 */
router.get('/image/:filename', UploadController.getImageInfo)

// 需要认证的路由
/**
 * POST /api/upload/image
 * 上传单个图片
 * 需要认证
 * 请求体：multipart/form-data
 * - image: 图片文件（必填）
 */
router.post('/image', authenticateToken, requirePermission('media.upload'), (req, res, next) => {
  UploadController.uploadSingle(req, res, (err) => {
    if (err) {
      res.status(400).json({
        success: false,
        message: '图片上传失败',
        error: err.message
      })
      return
    }
    next()
  })
}, UploadController.handleSingleUpload)

/**
 * POST /api/upload/images
 * 上传多个图片
 * 需要认证
 * 请求体：multipart/form-data
 * - images: 图片文件数组（必填，最多9张）
 */
router.post('/images', authenticateToken, requirePermission('media.upload'), (req, res, next) => {
  UploadController.uploadMultiple(req, res, (err) => {
    if (err) {
      res.status(400).json({
        success: false,
        message: '图片上传失败',
        error: err.message
      })
      return
    }
    next()
  })
}, UploadController.handleMultipleUpload)

/**
 * DELETE /api/upload/image/:filename
 * 删除图片
 * 需要认证
 * 参数：
 * - filename: 图片文件名
 */
router.delete('/image/:filename', authenticateToken, requirePermission('media.delete'), UploadController.deleteImage)

export default router
