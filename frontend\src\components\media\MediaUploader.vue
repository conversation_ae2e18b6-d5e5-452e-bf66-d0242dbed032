<template>
  <div class="media-uploader">
    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      class="media-uploader__upload"
      :class="{ 'media-uploader__upload--dragging': isDragging }"
      drag
      :multiple="multiple"
      :accept="acceptTypes"
      :before-upload="beforeUpload"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :on-exceed="handleExceed"
      :limit="maxFiles"
      :auto-upload="false"
      :show-file-list="false"
      @dragenter="isDragging = true"
      @dragleave="isDragging = false"
      @drop="isDragging = false"
    >
      <div class="media-uploader__content">
        <el-icon class="media-uploader__icon">
          <UploadFilled />
        </el-icon>
        <div class="media-uploader__text">
          <p class="media-uploader__title">
            {{ multiple ? '拖拽文件到此处或点击上传' : '拖拽文件到此处或点击上传' }}
          </p>
          <p class="media-uploader__subtitle">
            支持 {{ acceptedFormats.join(', ') }} 格式，
            单个文件最大 {{ formatFileSize(maxFileSize) }}
            {{ multiple ? `，最多 ${maxFiles} 个文件` : '' }}
          </p>
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="media-uploader__file-list">
      <div class="media-uploader__file-header">
        <span>待上传文件 ({{ fileList.length }})</span>
        <el-button
          type="danger"
          size="small"
          text
          @click="clearFiles"
        >
          清空
        </el-button>
      </div>

      <div class="media-uploader__files">
        <div
          v-for="(file, index) in fileList"
          :key="file.uid"
          class="media-uploader__file-item"
        >
          <!-- 文件预览 -->
          <div class="media-uploader__file-preview">
            <img
              v-if="file.preview && isImageFile(file.raw)"
              :src="file.preview"
              :alt="file.name"
              class="media-uploader__file-image"
            />
            <div v-else class="media-uploader__file-icon">
              <el-icon>
                <component :is="getFileIcon(file.raw)" />
              </el-icon>
            </div>
          </div>

          <!-- 文件信息 -->
          <div class="media-uploader__file-info">
            <div class="media-uploader__file-name" :title="file.name">
              {{ file.name }}
            </div>
            <div class="media-uploader__file-meta">
              <span>{{ formatFileSize(file.size || 0) }}</span>
              <span>{{ getFileCategory(file.raw) }}</span>
            </div>
            
            <!-- 上传进度 -->
            <div v-if="file.status === 'uploading'" class="media-uploader__file-progress">
              <el-progress
                :percentage="file.percentage || 0"
                :stroke-width="4"
                :show-text="false"
              />
            </div>
            
            <!-- 上传状态 -->
            <div v-else-if="file.status === 'success'" class="media-uploader__file-status success">
              <el-icon><CircleCheck /></el-icon>
              <span>上传成功</span>
            </div>
            
            <div v-else-if="file.status === 'fail'" class="media-uploader__file-status error">
              <el-icon><CircleClose /></el-icon>
              <span>上传失败</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="media-uploader__file-actions">
            <el-button
              type="danger"
              size="small"
              circle
              @click="removeFile(index)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传控制 -->
    <div v-if="fileList.length > 0" class="media-uploader__controls">
      <el-button
        type="primary"
        :loading="uploading"
        :disabled="fileList.length === 0"
        @click="startUpload"
      >
        {{ uploading ? '上传中...' : `开始上传 (${fileList.length})` }}
      </el-button>
      
      <el-button @click="clearFiles">
        取消
      </el-button>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading" class="media-uploader__progress">
      <div class="media-uploader__progress-info">
        <span>正在上传... {{ uploadedCount }}/{{ fileList.length }}</span>
        <span>{{ Math.round(totalProgress) }}%</span>
      </div>
      <el-progress
        :percentage="totalProgress"
        :stroke-width="6"
        :show-text="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadFile, UploadFiles, UploadRawFile, UploadInstance } from 'element-plus'
import {
  UploadFilled,
  Picture,
  VideoPlay,
  Headphone,
  Document,
  CircleCheck,
  CircleClose,
  Close
} from '@element-plus/icons-vue'
import { MediaUtils } from '@/services/media'
import { useMediaStore } from '@/stores/media'
import type { MediaCategory } from '@/types/media'

// ==================== 组件属性 ====================
interface Props {
  multiple?: boolean
  maxFiles?: number
  maxFileSize?: number
  accept?: MediaCategory[]
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  maxFiles: 9,
  maxFileSize: 5 * 1024 * 1024, // 5MB
  accept: () => ['image', 'video', 'audio', 'document'],
  autoUpload: false
})

// ==================== 组件事件 ====================
interface Emits {
  success: [files: any[]]
  error: [error: Error]
  progress: [progress: number]
}

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])
const uploading = ref(false)
const isDragging = ref(false)
const uploadedCount = ref(0)

// ==================== Store ====================
const mediaStore = useMediaStore()

// ==================== 计算属性 ====================
const acceptTypes = computed(() => {
  const typeMap: Record<MediaCategory, string[]> = {
    image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    video: ['video/mp4', 'video/webm', 'video/ogg'],
    audio: ['audio/mp3', 'audio/wav', 'audio/ogg'],
    document: ['application/pdf', 'text/plain', 'application/msword']
  }
  
  return props.accept.flatMap(category => typeMap[category]).join(',')
})

const acceptedFormats = computed(() => {
  const formatMap: Record<MediaCategory, string[]> = {
    image: ['JPG', 'PNG', 'GIF', 'WebP'],
    video: ['MP4', 'WebM', 'OGG'],
    audio: ['MP3', 'WAV', 'OGG'],
    document: ['PDF', 'TXT', 'DOC']
  }
  
  return props.accept.flatMap(category => formatMap[category])
})

const totalProgress = computed(() => {
  if (fileList.value.length === 0) return 0
  
  const totalPercentage = fileList.value.reduce((sum, file) => {
    return sum + (file.percentage || 0)
  }, 0)
  
  return totalPercentage / fileList.value.length
})

// ==================== 方法 ====================
/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  return MediaUtils.formatFileSize(bytes)
}

/**
 * 检查是否为图片文件
 */
const isImageFile = (file?: File): boolean => {
  return file ? MediaUtils.isImage(file.type) : false
}

/**
 * 获取文件类别
 */
const getFileCategory = (file?: File): string => {
  if (!file) return '未知'
  
  const categoryMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档'
  }
  
  return categoryMap[MediaUtils.getCategoryFromMimeType(file.type)]
}

/**
 * 获取文件图标
 */
const getFileIcon = (file?: File) => {
  if (!file) return Document
  
  const category = MediaUtils.getCategoryFromMimeType(file.type)
  const iconMap = {
    image: Picture,
    video: VideoPlay,
    audio: Headphone,
    document: Document
  }
  
  return iconMap[category]
}

/**
 * 生成文件预览
 */
const generatePreview = (file: File): Promise<string> => {
  return new Promise((resolve) => {
    if (isImageFile(file)) {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.readAsDataURL(file)
    } else {
      resolve('')
    }
  })
}

/**
 * 上传前检查
 */
const beforeUpload = (rawFile: UploadRawFile): boolean => {
  // 检查文件类型
  if (!MediaUtils.validateFileType(rawFile.type)) {
    ElMessage.error(`不支持的文件类型: ${rawFile.type}`)
    return false
  }
  
  // 检查文件大小
  if (rawFile.size > props.maxFileSize) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(props.maxFileSize)}`)
    return false
  }
  
  return true
}

/**
 * 文件变化处理
 */
const handleFileChange = async (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
  
  // 为新文件生成预览
  if (file.raw && !file.preview) {
    file.preview = await generatePreview(file.raw)
  }
  
  // 自动上传
  if (props.autoUpload) {
    await nextTick()
    startUpload()
  }
}

/**
 * 文件移除处理
 */
const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}

/**
 * 文件数量超限处理
 */
const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${props.maxFiles} 个文件`)
}

/**
 * 移除文件
 */
const removeFile = (index: number) => {
  fileList.value.splice(index, 1)
}

/**
 * 清空文件列表
 */
const clearFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
}

/**
 * 开始上传
 */
const startUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  try {
    uploading.value = true
    uploadedCount.value = 0
    
    const files = fileList.value.map(file => file.raw!).filter(Boolean)
    
    if (files.length === 1) {
      // 单文件上传
      const result = await mediaStore.uploadSingle(files[0])
      uploadedCount.value = 1
      emit('success', [result])
    } else {
      // 多文件上传
      const result = await mediaStore.uploadMultiple(files)
      uploadedCount.value = files.length
      emit('success', result?.files || [])
    }
    
    ElMessage.success(`成功上传 ${files.length} 个文件`)
    clearFiles()
    
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '上传失败')
    emit('error', error instanceof Error ? error : new Error('上传失败'))
  } finally {
    uploading.value = false
    uploadedCount.value = 0
  }
}
</script>

<style scoped lang="scss">
.media-uploader {
  &__upload {
    width: 100%;
    
    :deep(.el-upload) {
      width: 100%;
    }
    
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 200px;
      border: 2px dashed var(--el-border-color);
      border-radius: 8px;
      background: var(--el-fill-color-lighter);
      transition: all 0.3s ease;
      
      &:hover {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }
    }
    
    &--dragging {
      :deep(.el-upload-dragger) {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }
    }
  }
  
  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
  }
  
  &__icon {
    font-size: 48px;
    color: var(--el-color-primary);
    margin-bottom: 16px;
  }
  
  &__text {
    text-align: center;
  }
  
  &__title {
    font-size: 16px;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }
  
  &__subtitle {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0;
    line-height: 1.4;
  }
  
  &__file-list {
    margin-top: 20px;
  }
  
  &__file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  &__files {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  &__file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-light);
  }
  
  &__file-preview {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    overflow: hidden;
    background: var(--el-fill-color);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__file-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  &__file-icon {
    font-size: 24px;
    color: var(--el-text-color-secondary);
  }
  
  &__file-info {
    flex: 1;
    min-width: 0;
  }
  
  &__file-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 4px;
  }
  
  &__file-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  &__file-progress {
    margin-top: 8px;
  }
  
  &__file-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    margin-top: 4px;
    
    &.success {
      color: var(--el-color-success);
    }
    
    &.error {
      color: var(--el-color-danger);
    }
  }
  
  &__file-actions {
    display: flex;
    gap: 8px;
  }
  
  &__controls {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    justify-content: center;
  }
  
  &__progress {
    margin-top: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
  }
  
  &__progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
}
</style>
