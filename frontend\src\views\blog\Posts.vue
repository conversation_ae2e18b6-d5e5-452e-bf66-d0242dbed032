<template>
  <div class="blog-posts">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title">说说</h1>
          <p class="page-description">分享生活中的点点滴滴</p>
        </div>
        
        <!-- 发布按钮（已登录用户） -->
        <div v-if="isAuthenticated" class="header-actions">
          <el-button
            type="primary"
            :icon="Plus"
            @click="handleCreate"
          >
            发布说说
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="container">
        <div class="content-layout">
          <!-- 说说列表 -->
          <div class="posts-section">
            <PostList
              :posts="posts"
              :loading="loading"
              :pagination="pagination"
              :show-filters="true"
              :show-pagination="false"
              :show-visibility="false"
              :show-actions="false"
              :auto-load="true"
              @search="handleSearch"
              @filter-change="handleFilterChange"
              @load-more="handleLoadMore"
              @refresh="handleRefresh"
              @post-click="handlePostClick"
              @like="handleLike"
              @comment="handleComment"
            />
          </div>

          <!-- 侧边栏 -->
          <div class="sidebar">
            <!-- 统计信息 -->
            <el-card class="sidebar-card">
              <template #header>
                <div class="card-header">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>说说统计</span>
                </div>
              </template>
              <div class="stats-list">
                <div class="stat-item">
                  <span class="stat-label">总说说数</span>
                  <span class="stat-value">{{ stats.total }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">今日新增</span>
                  <span class="stat-value">{{ stats.todayCount }}</span>
                </div>
              </div>
            </el-card>

            <!-- 热门标签 -->
            <el-card class="sidebar-card">
              <template #header>
                <div class="card-header">
                  <el-icon><PriceTag /></el-icon>
                  <span>热门话题</span>
                </div>
              </template>
              <div class="tags-list">
                <el-tag
                  v-for="tag in hotTags"
                  :key="tag.name"
                  class="hot-tag"
                  @click="handleTagClick(tag.name)"
                >
                  {{ tag.name }}
                </el-tag>
              </div>
            </el-card>

            <!-- 最新评论 -->
            <el-card class="sidebar-card">
              <template #header>
                <div class="card-header">
                  <el-icon><ChatLineRound /></el-icon>
                  <span>最新评论</span>
                </div>
              </template>
              <div class="comments-list">
                <div
                  v-for="comment in recentComments"
                  :key="comment.id"
                  class="comment-item"
                  @click="handleCommentClick(comment)"
                >
                  <div class="comment-author">{{ comment.author?.username }}</div>
                  <div class="comment-content">{{ truncateText(comment.content, 50) }}</div>
                  <div class="comment-time">{{ formatDate(comment.createdAt) }}</div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 说说编辑器 -->
    <PostEditor
      v-model="showEditor"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />

    <!-- 回到顶部 -->
    <el-backtop :right="40" :bottom="40" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus,
  DataAnalysis,
  PriceTag,
  ChatLineRound
} from '@element-plus/icons-vue'
import { usePostStore } from '@/stores/post'
import { useAuthStore } from '@/stores/auth'
import { useTagStore } from '@/stores/tag'
import { commentService } from '@/services/comment'
import PostList from '@/components/blog/PostList.vue'
import PostEditor from '@/components/blog/PostEditor.vue'
import type { Post, PostCreateRequest, PostParams } from '@/services/types/post'
import type { Comment } from '@/services/types/comment'

/**
 * 路由和状态管理
 */
const router = useRouter()
const postStore = usePostStore()
const authStore = useAuthStore()
const tagStore = useTagStore()

/**
 * 响应式数据
 */
const showEditor = ref(false)
const recentComments = ref<Comment[]>([])
const currentFilters = ref<PostParams>({})

/**
 * 计算属性
 */
const posts = computed(() => postStore.posts)
const loading = computed(() => postStore.loading)
const pagination = computed(() => postStore.pagination)
const stats = computed(() => postStore.stats)
const isAuthenticated = computed(() => authStore.isAuthenticated)
const hotTags = computed(() => tagStore.tags.slice(0, 10))

/**
 * 生命周期
 */
onMounted(async () => {
  await loadInitialData()
  // 设置页面标题
  document.title = '说说 - 个人博客'
})

onUnmounted(() => {
  // 清理状态
  postStore.resetState()
})

/**
 * 数据加载
 */
const loadInitialData = async () => {
  try {
    await Promise.all([
      loadPosts(),
      loadStats(),
      loadHotTags(),
      loadRecentComments()
    ])
  } catch (error) {
    console.error('加载初始数据失败:', error)
  }
}

const loadPosts = async (page = 1, append = false) => {
  try {
    const params = {
      ...currentFilters.value,
      visibility: 'public' // 前台只显示公开说说
    }
    
    if (append) {
      // 加载更多模式
      const response = await postStore.fetchPosts(page, pagination.value.itemsPerPage, params)
      // 手动追加数据
      postStore.posts.push(...response.posts)
    } else {
      await postStore.fetchPosts(page, pagination.value.itemsPerPage, params)
    }
  } catch (error) {
    console.error('加载说说列表失败:', error)
    ElMessage.error('加载说说列表失败')
  }
}

const loadStats = async () => {
  try {
    await postStore.fetchStats()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadHotTags = async () => {
  try {
    await tagStore.fetchTags()
  } catch (error) {
    console.error('加载热门标签失败:', error)
  }
}

const loadRecentComments = async () => {
  try {
    const response = await commentService.getComments({
      page: 1,
      limit: 5,
      sort: 'createdAt',
      order: 'desc'
    })
    recentComments.value = response.comments
  } catch (error) {
    console.error('加载最新评论失败:', error)
  }
}

/**
 * 事件处理
 */
const handleCreate = () => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  showEditor.value = true
}

const handleSubmit = async (data: PostCreateRequest) => {
  try {
    await postStore.createPost(data)
    ElMessage.success('说说发布成功')
    showEditor.value = false
    await loadStats()
  } catch (error) {
    console.error('发布说说失败:', error)
  }
}

const handleCancel = () => {
  showEditor.value = false
}

const handleSearch = (query: string) => {
  currentFilters.value.search = query
  loadPosts(1)
}

const handleFilterChange = (filters: PostParams) => {
  currentFilters.value = { ...filters, visibility: 'public' }
  loadPosts(1)
}

const handleLoadMore = () => {
  if (pagination.value.hasNextPage) {
    loadPosts(pagination.value.currentPage + 1, true)
  }
}

const handleRefresh = () => {
  loadPosts(1)
}

const handlePostClick = (post: Post) => {
  router.push(`/posts/${post.id}`)
}

const handleLike = async (postId: number) => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  try {
    await postStore.toggleLike(postId)
  } catch (error) {
    console.error('点赞操作失败:', error)
  }
}

const handleComment = (postId: number) => {
  router.push(`/posts/${postId}#comments`)
}

const handleTagClick = (tagName: string) => {
  currentFilters.value.search = `#${tagName}`
  loadPosts(1)
}

const handleCommentClick = (comment: Comment) => {
  if (comment.postId) {
    router.push(`/posts/${comment.postId}#comment-${comment.id}`)
  }
}

/**
 * 工具函数
 */
const formatDate = (date: string) => {
  const now = new Date()
  const commentDate = new Date(date)
  const diff = now.getTime() - commentDate.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return commentDate.toLocaleDateString('zh-CN')
}

const truncateText = (text: string, maxLength: number) => {
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text
}
</script>

<style scoped>
.blog-posts {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.page-description {
  font-size: 16px;
  color: var(--el-text-color-secondary);
  margin: 0;
}

.main-content {
  padding: 40px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.posts-section {
  min-height: 600px;
}

.sidebar {
  position: sticky;
  top: 20px;
  height: fit-content;
}

.sidebar-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--el-text-color-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--el-color-primary);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.hot-tag:hover {
  background: var(--el-color-primary);
  color: white;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comment-item {
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.comment-item:hover {
  background: var(--el-fill-color-lighter);
}

.comment-author {
  font-size: 12px;
  font-weight: 500;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.comment-content {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.comment-time {
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .sidebar {
    position: static;
    order: -1;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
