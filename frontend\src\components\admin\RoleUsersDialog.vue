<template>
  <el-dialog
    :model-value="modelValue"
    :title="`角色用户列表 - ${role?.name}`"
    width="800px"
    @update:model-value="$emit('update:modelValue', $event)"
    @open="handleOpen"
  >
    <div v-loading="loading" class="role-users-dialog">
      <!-- 操作栏 -->
      <div class="action-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户名或邮箱"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <!-- 用户表格 -->
      <el-table
        :data="userRoles"
        stripe
        style="width: 100%; margin-top: 16px"
      >
        <el-table-column prop="user.username" label="用户名" min-width="120" />
        
        <el-table-column prop="user.email" label="邮箱" min-width="180" />
        
        <el-table-column prop="user.nickname" label="昵称" min-width="120">
          <template #default="{ row }">
            {{ row.user?.nickname || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="assignedByUser" label="分配者" min-width="120">
          <template #default="{ row }">
            {{ row.assignedByUser?.nickname || row.assignedByUser?.username || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="assignedAt" label="分配时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.assignedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-popconfirm
              title="确定要移除这个用户的角色吗？"
              @confirm="handleRemoveRole(row)"
            >
              <template #reference>
                <el-button type="danger" size="small">
                  移除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
      
      <!-- 统计信息 -->
      <div class="summary-info">
        <el-card>
          <template #header>
            <span>统计信息</span>
          </template>
          <div class="summary-content">
            <div class="summary-item">
              <span class="label">总用户数：</span>
              <span class="value">{{ pagination.total }}</span>
            </div>
            <div class="summary-item">
              <span class="label">当前页用户：</span>
              <span class="value">{{ userRoles.length }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { useRbacStore } from '@/stores/rbac'
import { formatDate } from '@/utils/date'
import type { Role, UserRole } from '@/services/rbac'

// Props & Emits
interface Props {
  modelValue: boolean
  role: Role | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// Store
const rbacStore = useRbacStore()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const userRoles = ref<UserRole[]>([])

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

// 方法
const loadRoleUsers = async () => {
  if (!props.role) return
  
  try {
    loading.value = true
    
    const response = await rbacStore.fetchUserRoles({
      roleId: props.role.id,
      page: pagination.page,
      limit: pagination.limit,
      search: searchKeyword.value || undefined
    })
    
    userRoles.value = rbacStore.userRoles
    Object.assign(pagination, rbacStore.userRolesPagination)
  } catch (error) {
    console.error('加载角色用户失败:', error)
    ElMessage.error('加载角色用户失败')
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  if (props.role) {
    pagination.page = 1
    searchKeyword.value = ''
    loadRoleUsers()
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadRoleUsers()
}

const handleReset = () => {
  searchKeyword.value = ''
  pagination.page = 1
  loadRoleUsers()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadRoleUsers()
}

const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  loadRoleUsers()
}

const handleRemoveRole = async (userRole: UserRole) => {
  try {
    const success = await rbacStore.removeUserRole(userRole.id)
    if (success) {
      // 重新加载数据
      loadRoleUsers()
      ElMessage.success('角色移除成功')
    }
  } catch (error) {
    console.error('移除角色失败:', error)
    ElMessage.error('移除角色失败')
  }
}

// 监听对话框打开状态
watch(() => props.modelValue, (visible) => {
  if (visible && props.role) {
    handleOpen()
  }
})
</script>

<style scoped>
.role-users-dialog {
  min-height: 400px;
}

.action-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 24px 0;
}

.summary-info {
  margin-top: 16px;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item .label {
  color: var(--el-text-color-regular);
}

.summary-item .value {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
