<template>
  <div class="admin-articles-view">
    <!-- 页面头部 -->
    <header class="admin-header">
      <h1>文章管理</h1>
      <div class="user-info">
        <span v-if="authStore.user">欢迎, {{ authStore.user.username }}</span>
        <button @click="handleLogout" class="logout-button">退出登录</button>
      </div>
    </header>

    <!-- 导航栏 -->
    <nav class="admin-nav">
      <router-link to="/admin" class="nav-link">仪表板</router-link>
      <router-link to="/admin/articles" class="nav-link">文章管理</router-link>
      <router-link to="/admin/posts" class="nav-link">说说管理</router-link>
      <router-link to="/admin/media" class="nav-link">媒体管理</router-link>
      <router-link to="/admin/categories" class="nav-link">分类管理</router-link>
      <router-link to="/admin/tags" class="nav-link">标签管理</router-link>
      <router-link to="/admin/comments" class="nav-link">评论管理</router-link>
      <router-link to="/admin/notifications" class="nav-link">通知中心</router-link>
      <router-link to="/admin/roles" class="nav-link">角色管理</router-link>
      <router-link to="/admin/user-roles" class="nav-link">用户角色</router-link>
      <router-link to="/settings" class="nav-link">设置</router-link>
    </nav>

    <!-- 主内容区域 -->
    <main class="admin-content">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ articleStats.total }}</div>
              <div class="stat-label">总文章数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon published">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ articleStats.published }}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon draft">
              <el-icon><Edit /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ articleStats.draft }}</div>
              <div class="stat-label">草稿</div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <el-button
            type="primary"
            :icon="Plus"
            @click="createNewArticle"
            size="default"
          >
            新建文章
          </el-button>
        </div>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filters-section">
        <div class="filters-row">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文章标题、内容..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              @clear="handleClearSearch"
              class="search-input"
            />
          </div>

          <!-- 筛选器 -->
          <div class="filter-controls">
            <el-select
              v-model="statusFilter"
              placeholder="状态"
              clearable
              @change="handleFilterChange"
              class="filter-select"
            >
              <el-option label="全部状态" value="" />
              <el-option label="已发布" value="published" />
              <el-option label="草稿" value="draft" />
            </el-select>

            <el-select
              v-model="tagFilter"
              placeholder="标签"
              clearable
              filterable
              @change="handleFilterChange"
              class="filter-select"
            >
              <el-option label="全部标签" value="" />
              <el-option
                v-for="tag in availableTags"
                :key="tag.id"
                :label="tag.name"
                :value="tag.slug"
              />
            </el-select>

            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilterChange"
              class="date-picker"
              size="default"
            />
          </div>
        </div>

        <!-- 批量操作栏 -->
        <div v-if="selectedArticles.length > 0" class="batch-actions">
          <div class="batch-info">
            <span>已选择 {{ selectedArticles.length }} 篇文章</span>
          </div>
          <div class="batch-buttons">
            <el-button
              @click="batchPublish"
              :icon="Promotion"
              type="success"
              size="small"
            >
              批量发布
            </el-button>
            <el-button
              @click="batchDraft"
              :icon="Edit"
              size="small"
            >
              设为草稿
            </el-button>
            <el-button
              @click="batchDelete"
              :icon="Delete"
              type="danger"
              size="small"
            >
              批量删除
            </el-button>
            <el-button
              @click="clearSelection"
              :icon="Close"
              size="small"
            >
              取消选择
            </el-button>
          </div>
        </div>
      </div>

      <!-- 文章列表表格 -->
      <div class="table-section">
        <el-table
          ref="articleTable"
          v-loading="loading"
          :data="articles"
          @selection-change="handleSelectionChange"
          class="articles-table"
          stripe
          border
        >
          <!-- 选择列 -->
          <el-table-column type="selection" width="55" />

          <!-- 标题列 -->
          <el-table-column prop="title" label="标题" min-width="200">
            <template #default="{ row }">
              <div class="title-cell">
                <router-link
                  :to="`/admin/articles/${row.id}/edit`"
                  class="article-title"
                >
                  {{ row.title }}
                </router-link>
                <div class="article-meta">
                  <span class="article-id">ID: {{ row.id }}</span>
                  <span v-if="row.slug" class="article-slug">{{ row.slug }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 'published' ? 'success' : 'warning'"
                effect="light"
              >
                <el-icon>
                  <component :is="row.status === 'published' ? Check : Edit" />
                </el-icon>
                {{ row.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 标签列 -->
          <el-table-column prop="tags" label="标签" width="200">
            <template #default="{ row }">
              <div class="tags-cell">
                <el-tag
                  v-for="tag in row.tags"
                  :key="tag.id"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.name }}
                </el-tag>
                <span v-if="!row.tags || row.tags.length === 0" class="no-tags">
                  无标签
                </span>
              </div>
            </template>
          </el-table-column>

          <!-- 创建时间列 -->
          <el-table-column prop="createdAt" label="创建时间" width="160">
            <template #default="{ row }">
              <div class="time-cell">
                <div>{{ formatDate(row.createdAt) }}</div>
                <div class="time-detail">{{ formatTime(row.createdAt) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 更新时间列 -->
          <el-table-column prop="updatedAt" label="更新时间" width="160">
            <template #default="{ row }">
              <div class="time-cell">
                <div>{{ formatDate(row.updatedAt) }}</div>
                <div class="time-detail">{{ formatTime(row.updatedAt) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 字数统计列 -->
          <el-table-column prop="content" label="字数" width="80">
            <template #default="{ row }">
              <span class="word-count">{{ getWordCount(row.content) }}</span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  :icon="Edit"
                  @click="editArticle(row.id)"
                  type="primary"
                  size="small"
                  link
                >
                  编辑
                </el-button>
                <el-button
                  :icon="View"
                  @click="previewArticle(row)"
                  size="small"
                  link
                >
                  预览
                </el-button>
                <el-button
                  :icon="Delete"
                  @click="deleteArticle(row)"
                  type="danger"
                  size="small"
                  link
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination"
          />
        </div>
      </div>
    </main>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
      center
    >
      <div class="delete-dialog-content">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <div class="dialog-text">
          <p v-if="articleToDelete">确定要删除文章《{{ articleToDelete.title }}》吗？</p>
          <p v-else-if="selectedArticles.length > 0">确定要删除选中的 {{ selectedArticles.length }} 篇文章吗？</p>
          <p class="warning-text">此操作不可恢复！</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          @click="confirmDelete"
          :loading="deleting"
        >
          {{ deleting ? '删除中...' : '确认删除' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type ElTable } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useArticleStore, type Article } from '@/stores/article'
import { useTagStore } from '@/stores/tag'
import {
  Document,
  Check,
  Edit,
  Plus,
  Search,
  Promotion,
  Delete,
  Close,
  View,
  WarningFilled
} from '@element-plus/icons-vue'

// Stores
const router = useRouter()
const authStore = useAuthStore()
const articleStore = useArticleStore()
const tagStore = useTagStore()

// Refs
const articleTable = ref<InstanceType<typeof ElTable>>()
const loading = ref(false)
const deleting = ref(false)

// 筛选和搜索状态
const searchQuery = ref('')
const statusFilter = ref<'' | 'draft' | 'published'>('')
const tagFilter = ref('')
const dateRange = ref<[Date, Date] | null>(null)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)

// 选择状态
const selectedArticles = ref<Article[]>([])

// 删除对话框状态
const deleteDialogVisible = ref(false)
const articleToDelete = ref<Article | null>(null)

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// Computed properties
const articles = computed(() => articleStore.articles)
const totalItems = computed(() => articleStore.pagination.totalItems)
const availableTags = computed(() => tagStore.tags)

// 文章统计
const articleStats = computed(() => {
  const total = articles.value.length
  const published = articles.value.filter(article => article.status === 'published').length
  const draft = articles.value.filter(article => article.status === 'draft').length

  return {
    total,
    published,
    draft
  }
})

// Methods
const handleLogout = () => {
  try {
    authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
    ElMessage.error('登出失败，请重试')
  }
}

const createNewArticle = () => {
  router.push('/admin/articles/new')
}

const editArticle = (id: number) => {
  router.push(`/admin/articles/${id}/edit`)
}

const previewArticle = (article: Article) => {
  // 在新窗口打开文章预览
  const url = `/article/${article.id}`
  window.open(url, '_blank')
}

// 搜索处理
const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    fetchArticles()
  }, 500) // 500ms 防抖
}

const handleClearSearch = () => {
  searchQuery.value = ''
  fetchArticles()
}

// 筛选处理
const handleFilterChange = () => {
  currentPage.value = 1 // 重置到第一页
  fetchArticles()
}

// 分页处理
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchArticles()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchArticles()
}

// 选择处理
const handleSelectionChange = (selection: Article[]) => {
  selectedArticles.value = selection
}

const clearSelection = () => {
  articleTable.value?.clearSelection()
  selectedArticles.value = []
}

// 批量操作
const batchPublish = async () => {
  if (selectedArticles.value.length === 0) return

  try {
    const result = await ElMessageBox.confirm(
      `确定要将选中的 ${selectedArticles.value.length} 篇文章设为已发布吗？`,
      '批量发布',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      loading.value = true

      // 批量更新文章状态
      const updatePromises = selectedArticles.value.map(article =>
        articleStore.updateArticle(article.id, {
          ...article,
          status: 'published' as const,
          publishedAt: article.status === 'draft' ? new Date().toISOString() : article.publishedAt
        })
      )

      await Promise.all(updatePromises)

      ElMessage.success(`成功发布 ${selectedArticles.value.length} 篇文章`)
      clearSelection()
      await fetchArticles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量发布失败:', error)
      ElMessage.error('批量发布失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

const batchDraft = async () => {
  if (selectedArticles.value.length === 0) return

  try {
    const result = await ElMessageBox.confirm(
      `确定要将选中的 ${selectedArticles.value.length} 篇文章设为草稿吗？`,
      '批量设为草稿',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      loading.value = true

      // 批量更新文章状态
      const updatePromises = selectedArticles.value.map(article =>
        articleStore.updateArticle(article.id, {
          ...article,
          status: 'draft' as const
        })
      )

      await Promise.all(updatePromises)

      ElMessage.success(`成功将 ${selectedArticles.value.length} 篇文章设为草稿`)
      clearSelection()
      await fetchArticles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量设为草稿失败:', error)
      ElMessage.error('批量设为草稿失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

const batchDelete = () => {
  if (selectedArticles.value.length === 0) return

  articleToDelete.value = null
  deleteDialogVisible.value = true
}

// 删除操作
const deleteArticle = (article: Article) => {
  articleToDelete.value = article
  deleteDialogVisible.value = true
}

const confirmDelete = async () => {
  try {
    deleting.value = true

    if (articleToDelete.value) {
      // 单个删除
      await articleStore.deleteArticle(articleToDelete.value.id)
      ElMessage.success('文章删除成功')
    } else if (selectedArticles.value.length > 0) {
      // 批量删除
      const deletePromises = selectedArticles.value.map(article =>
        articleStore.deleteArticle(article.id)
      )
      await Promise.all(deletePromises)
      ElMessage.success(`成功删除 ${selectedArticles.value.length} 篇文章`)
      clearSelection()
    }

    deleteDialogVisible.value = false
    articleToDelete.value = null
    await fetchArticles()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败，请重试')
  } finally {
    deleting.value = false
  }
}

// 工具方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getWordCount = (content: string) => {
  if (!content) return 0
  // 移除HTML标签并计算字数
  const plainText = content.replace(/<[^>]*>/g, '').trim()
  return plainText.length
}

// 获取文章列表
const fetchArticles = async () => {
  try {
    loading.value = true

    const params: any = {
      page: currentPage.value,
      limit: pageSize.value
    }

    // 添加搜索条件
    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }

    // 添加状态筛选
    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    // 添加标签筛选
    if (tagFilter.value) {
      params.tag = tagFilter.value
    }

    // 添加日期范围筛选
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0].toISOString()
      params.endDate = dateRange.value[1].toISOString()
    }

    await articleStore.fetchArticles(
      currentPage.value,
      pageSize.value,
      params
    )
  } catch (error) {
    console.error('获取文章列表失败:', error)
    ElMessage.error('获取文章列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 检查认证状态
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 验证token是否有效
  const isValid = await authStore.checkAuth()
  if (!isValid) {
    router.push('/login')
    return
  }

  // 加载标签列表
  await tagStore.fetchTags()

  // 加载文章列表
  await fetchArticles()
})

// 监听筛选条件变化
watch([searchQuery, statusFilter, tagFilter, dateRange], () => {
  // 重置到第一页
  currentPage.value = 1
  // 防抖处理
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    fetchArticles()
  }, 300)
})
</script>

<style scoped>
/* ===== 后台文章管理页面样式 - 基于 Element Plus 设计系统 ===== */

.admin-articles-view {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
}

/* === 页面头部样式 === */
.admin-header {
  background: var(--el-bg-color);
  padding: var(--el-spacing-large) var(--el-spacing-extra-large);
  box-shadow: var(--el-box-shadow-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--el-border-color);
}

.admin-header h1 {
  color: var(--el-text-color-primary);
  margin: 0;
  font-size: var(--el-font-size-extra-large);
  font-weight: var(--el-font-weight-bold);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--el-spacing-medium);
}

.user-info span {
  color: var(--el-text-color-regular);
  font-size: var(--el-font-size-small);
}

.logout-button {
  padding: var(--el-spacing-small) var(--el-spacing-medium);
  background-color: var(--el-color-danger);
  color: var(--el-color-white);
  border: none;
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  font-size: var(--el-font-size-small);
  transition: background-color var(--el-transition-duration);
}

.logout-button:hover {
  background-color: var(--el-color-danger-dark-2);
}

/* === 导航栏样式 === */
.admin-nav {
  background: var(--el-bg-color);
  padding: 0 var(--el-spacing-extra-large);
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  gap: var(--el-spacing-extra-large);
}

.nav-link {
  padding: var(--el-spacing-medium) 0;
  text-decoration: none;
  color: var(--el-text-color-regular);
  border-bottom: 2px solid transparent;
  transition: all var(--el-transition-duration);
  font-weight: var(--el-font-weight-medium);
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--el-color-primary);
  border-bottom-color: var(--el-color-primary);
}

/* === 主内容区域 === */
.admin-content {
  padding: var(--el-spacing-extra-large);
  max-width: 1400px;
  margin: 0 auto;
}

/* === 统计卡片样式 === */
.stats-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--el-spacing-extra-large);
  gap: var(--el-spacing-large);
}

.stats-cards {
  display: flex;
  gap: var(--el-spacing-large);
  flex: 1;
}

.stat-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: var(--el-spacing-large);
  display: flex;
  align-items: center;
  gap: var(--el-spacing-medium);
  box-shadow: var(--el-box-shadow-light);
  transition: all var(--el-transition-duration);
  min-width: 160px;
}

.stat-card:hover {
  box-shadow: var(--el-box-shadow);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--el-border-radius-round);
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.published {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.stat-icon.draft {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--el-font-size-extra-large);
  font-weight: var(--el-font-weight-bold);
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: var(--el-spacing-small);
}

.stat-label {
  font-size: var(--el-font-size-small);
  color: var(--el-text-color-regular);
  line-height: 1;
}

.quick-actions {
  display: flex;
  align-items: flex-start;
}

/* === 筛选区域样式 === */
.filters-section {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: var(--el-spacing-large);
  margin-bottom: var(--el-spacing-large);
}

.filters-row {
  display: flex;
  gap: var(--el-spacing-medium);
  align-items: flex-start;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
}

.filter-controls {
  display: flex;
  gap: var(--el-spacing-medium);
  flex-wrap: wrap;
}

.filter-select {
  width: 150px;
}

.date-picker {
  width: 280px;
}

/* === 批量操作栏样式 === */
.batch-actions {
  margin-top: var(--el-spacing-medium);
  padding-top: var(--el-spacing-medium);
  border-top: 1px solid var(--el-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--el-spacing-medium);
}

.batch-info {
  color: var(--el-text-color-regular);
  font-size: var(--el-font-size-small);
  font-weight: var(--el-font-weight-medium);
}

.batch-buttons {
  display: flex;
  gap: var(--el-spacing-small);
  flex-wrap: wrap;
}

/* === 表格区域样式 === */
.table-section {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
}

.articles-table {
  width: 100%;
}

/* === 表格单元格样式 === */
.title-cell {
  padding: var(--el-spacing-small) 0;
}

.article-title {
  color: var(--el-color-primary);
  text-decoration: none;
  font-weight: var(--el-font-weight-medium);
  font-size: var(--el-font-size-base);
  line-height: 1.4;
  display: block;
  margin-bottom: var(--el-spacing-extra-small);
}

.article-title:hover {
  color: var(--el-color-primary-dark-2);
  text-decoration: underline;
}

.article-meta {
  display: flex;
  gap: var(--el-spacing-medium);
  font-size: var(--el-font-size-extra-small);
  color: var(--el-text-color-placeholder);
}

.article-id,
.article-slug {
  font-family: var(--el-font-family-mono, 'Courier New', monospace);
}

.tags-cell {
  display: flex;
  flex-wrap: wrap;
  gap: var(--el-spacing-extra-small);
  align-items: center;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: var(--el-text-color-placeholder);
  font-size: var(--el-font-size-small);
  font-style: italic;
}

.time-cell {
  font-size: var(--el-font-size-small);
  line-height: 1.4;
}

.time-detail {
  color: var(--el-text-color-placeholder);
  font-size: var(--el-font-size-extra-small);
  margin-top: var(--el-spacing-extra-small);
}

.word-count {
  font-family: var(--el-font-family-mono, 'Courier New', monospace);
  font-weight: var(--el-font-weight-medium);
  color: var(--el-text-color-regular);
}

.action-buttons {
  display: flex;
  gap: var(--el-spacing-small);
  flex-wrap: wrap;
}

/* === 分页样式 === */
.pagination-section {
  padding: var(--el-spacing-large);
  border-top: 1px solid var(--el-border-color);
  background: var(--el-bg-color-page);
  display: flex;
  justify-content: center;
}

.pagination {
  --el-pagination-font-size: var(--el-font-size-small);
}

/* === 删除对话框样式 === */
.delete-dialog-content {
  display: flex;
  align-items: flex-start;
  gap: var(--el-spacing-medium);
  padding: var(--el-spacing-medium) 0;
}

.warning-icon {
  color: var(--el-color-warning);
  font-size: 24px;
  flex-shrink: 0;
  margin-top: var(--el-spacing-extra-small);
}

.dialog-text {
  flex: 1;
}

.dialog-text p {
  margin: 0 0 var(--el-spacing-small) 0;
  color: var(--el-text-color-primary);
  line-height: 1.5;
}

.warning-text {
  color: var(--el-color-danger) !important;
  font-size: var(--el-font-size-small) !important;
  font-weight: var(--el-font-weight-medium);
}

/* === 响应式设计 === */
@media (max-width: 1200px) {
  .admin-content {
    padding: var(--el-spacing-large);
  }

  .stats-cards {
    flex-wrap: wrap;
  }

  .stat-card {
    min-width: 140px;
  }
}

@media (max-width: 768px) {
  .admin-header {
    flex-direction: column;
    gap: var(--el-spacing-medium);
    align-items: stretch;
    text-align: center;
  }

  .admin-nav {
    padding: 0 var(--el-spacing-large);
    justify-content: center;
  }

  .admin-content {
    padding: var(--el-spacing-medium);
  }

  .stats-section {
    flex-direction: column;
    gap: var(--el-spacing-medium);
  }

  .stats-cards {
    justify-content: center;
  }

  .filters-row {
    flex-direction: column;
  }

  .search-box {
    min-width: auto;
  }

  .filter-controls {
    justify-content: center;
  }

  .filter-select {
    width: 120px;
  }

  .date-picker {
    width: 100%;
    max-width: 280px;
  }

  .batch-actions {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .batch-buttons {
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
  }

  /* 移动端表格优化 */
  .articles-table {
    font-size: var(--el-font-size-small);
  }

  .article-meta {
    flex-direction: column;
    gap: var(--el-spacing-extra-small);
  }

  .tags-cell {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .admin-header h1 {
    font-size: var(--el-font-size-large);
  }

  .stat-card {
    min-width: 120px;
    padding: var(--el-spacing-medium);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-number {
    font-size: var(--el-font-size-large);
  }

  .filter-select {
    width: 100px;
  }

  .pagination-section {
    padding: var(--el-spacing-medium);
  }
}

/* === 主题适配 === */
[data-theme="dark"] .admin-articles-view {
  background-color: var(--el-bg-color-page);
}

[data-theme="dark"] .stat-card:hover {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .article-title:hover {
  color: var(--el-color-primary-light-3);
}

/* === 加载状态优化 === */
.articles-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] .articles-table .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.8);
}

/* === 表格行悬停效果 === */
.articles-table .el-table__row:hover {
  background-color: var(--el-table-row-hover-bg-color);
}

/* === 选择状态样式 === */
.articles-table .el-table__row.current-row {
  background-color: var(--el-color-primary-light-9);
}

[data-theme="dark"] .articles-table .el-table__row.current-row {
  background-color: rgba(64, 158, 255, 0.1);
}
</style>