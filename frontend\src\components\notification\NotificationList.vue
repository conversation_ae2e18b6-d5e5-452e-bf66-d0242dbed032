<template>
  <div class="notification-list">
    <!-- 工具栏 -->
    <div class="notification-list__toolbar">
      <!-- 批量操作 -->
      <div class="notification-list__batch-actions">
        <el-checkbox 
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选
        </el-checkbox>
        
        <el-button 
          v-if="selectedNotifications.length > 0"
          type="primary"
          size="small"
          @click="markSelectedAsRead"
          :loading="batchLoading"
        >
          标记已读 ({{ selectedNotifications.length }})
        </el-button>
        
        <el-button 
          v-if="selectedNotifications.length > 0"
          type="danger"
          size="small"
          @click="deleteSelected"
          :loading="batchLoading"
        >
          删除 ({{ selectedNotifications.length }})
        </el-button>
      </div>

      <!-- 快捷操作 -->
      <div class="notification-list__quick-actions">
        <el-button 
          type="primary"
          size="small"
          @click="markAllAsRead"
          :disabled="unreadCount === 0"
          :loading="markingAllRead"
        >
          全部已读
        </el-button>
        
        <el-button 
          size="small"
          @click="refreshList"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 过滤器 -->
    <div class="notification-list__filters">
      <el-select 
        v-model="currentFilter.type"
        placeholder="通知类型"
        clearable
        size="small"
        style="width: 120px"
        @change="applyFilter"
      >
        <el-option label="互动通知" value="interaction" />
        <el-option label="内容通知" value="content" />
        <el-option label="系统通知" value="system" />
        <el-option label="营销通知" value="marketing" />
      </el-select>

      <el-select 
        v-model="currentFilter.priority"
        placeholder="优先级"
        clearable
        size="small"
        style="width: 100px"
        @change="applyFilter"
      >
        <el-option label="高优先级" value="high" />
        <el-option label="中优先级" value="medium" />
        <el-option label="低优先级" value="low" />
      </el-select>

      <el-select 
        v-model="currentFilter.isRead"
        placeholder="状态"
        clearable
        size="small"
        style="width: 100px"
        @change="applyFilter"
      >
        <el-option label="未读" :value="false" />
        <el-option label="已读" :value="true" />
      </el-select>

      <el-button 
        v-if="hasActiveFilter"
        size="small"
        @click="clearFilter"
      >
        清除过滤
      </el-button>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list__content">
      <!-- 加载状态 -->
      <div v-if="loading && notifications.length === 0" class="notification-list__loading">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="notifications.length === 0" class="notification-list__empty">
        <el-empty description="暂无通知" />
      </div>

      <!-- 通知项 -->
      <div v-else class="notification-list__items">
        <div 
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-list__item"
        >
          <el-checkbox 
            v-model="selectedIds"
            :label="notification.id"
            class="notification-list__checkbox"
          />
          <NotificationCard 
            :notification="notification"
            class="notification-list__card"
          />
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="notification-list__load-more">
        <el-button 
          @click="loadMore"
          :loading="loading"
          type="primary"
          plain
        >
          加载更多
        </el-button>
      </div>

      <!-- 底部加载状态 -->
      <div v-if="loading && notifications.length > 0" class="notification-list__loading-more">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import NotificationCard from './NotificationCard.vue'
import type { NotificationFilter } from '@/types/notification'

const notificationStore = useNotificationStore()

// 响应式状态
const selectedIds = ref<number[]>([])
const batchLoading = ref(false)
const markingAllRead = ref(false)
const currentFilter = ref<NotificationFilter>({})

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const loading = computed(() => notificationStore.loading)
const hasMore = computed(() => notificationStore.hasMore)
const unreadCount = computed(() => notificationStore.unreadCount)

const selectedNotifications = computed(() => 
  notifications.value.filter(n => selectedIds.value.includes(n.id))
)

const selectAll = computed({
  get: () => notifications.value.length > 0 && selectedIds.value.length === notifications.value.length,
  set: (value: boolean) => {
    if (value) {
      selectedIds.value = notifications.value.map(n => n.id)
    } else {
      selectedIds.value = []
    }
  }
})

const isIndeterminate = computed(() => 
  selectedIds.value.length > 0 && selectedIds.value.length < notifications.value.length
)

const hasActiveFilter = computed(() => 
  currentFilter.value.type || 
  currentFilter.value.priority !== undefined || 
  currentFilter.value.isRead !== undefined
)

// 方法
const handleSelectAll = (value: boolean) => {
  selectAll.value = value
}

const markSelectedAsRead = async () => {
  if (selectedIds.value.length === 0) return
  
  try {
    batchLoading.value = true
    await notificationStore.markBatchAsRead(selectedIds.value)
    selectedIds.value = []
  } catch (error) {
    console.error('批量标记已读失败:', error)
  } finally {
    batchLoading.value = false
  }
}

const deleteSelected = async () => {
  if (selectedIds.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 条通知吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    batchLoading.value = true
    await notificationStore.deleteBatchNotifications(selectedIds.value)
    selectedIds.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
    }
  } finally {
    batchLoading.value = false
  }
}

const markAllAsRead = async () => {
  if (unreadCount.value === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要将所有 ${unreadCount.value} 条未读通知标记为已读吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    markingAllRead.value = true
    await notificationStore.markAllAsRead()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('标记所有已读失败:', error)
    }
  } finally {
    markingAllRead.value = false
  }
}

const refreshList = async () => {
  selectedIds.value = []
  await notificationStore.refreshNotifications()
}

const loadMore = async () => {
  await notificationStore.loadMoreNotifications()
}

const applyFilter = async () => {
  selectedIds.value = []
  await notificationStore.applyFilter(currentFilter.value)
}

const clearFilter = async () => {
  currentFilter.value = {}
  selectedIds.value = []
  await notificationStore.clearFilter()
}

// 监听通知列表变化，清除无效的选中项
watch(notifications, (newNotifications) => {
  const validIds = newNotifications.map(n => n.id)
  selectedIds.value = selectedIds.value.filter(id => validIds.includes(id))
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  if (notifications.value.length === 0) {
    notificationStore.initialize()
  }
})
</script>

<style scoped>
.notification-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notification-list__toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.notification-list__batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-list__quick-actions {
  display: flex;
  gap: 8px;
}

.notification-list__filters {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
}

.notification-list__content {
  min-height: 200px;
}

.notification-list__loading,
.notification-list__empty {
  padding: 40px 16px;
}

.notification-list__items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-list__item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification-list__checkbox {
  margin-top: 20px;
  flex-shrink: 0;
}

.notification-list__card {
  flex: 1;
}

.notification-list__load-more {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.notification-list__loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 20px;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-list__toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .notification-list__batch-actions,
  .notification-list__quick-actions {
    justify-content: center;
  }
  
  .notification-list__filters {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .notification-list__item {
    gap: 8px;
  }
  
  .notification-list__checkbox {
    margin-top: 16px;
  }
}
</style>
