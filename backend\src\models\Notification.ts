import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { User } from './User'

/**
 * 通知模型的属性接口定义
 */
export interface NotificationAttributes {
  id: number
  type: 'interaction' | 'content' | 'system' | 'marketing'
  title: string
  content?: string
  priority: 'high' | 'medium' | 'low'
  recipientId: number
  senderId?: number
  relatedType?: 'article' | 'post' | 'comment' | 'user' | 'system'
  relatedId?: number
  actionUrl?: string
  isRead: boolean
  readAt?: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * 通知创建时的属性接口定义，部分字段为可选
 */
export interface NotificationCreationAttributes extends Optional<NotificationAttributes, 'id' | 'content' | 'senderId' | 'relatedType' | 'relatedId' | 'actionUrl' | 'isRead' | 'readAt' | 'createdAt' | 'updatedAt'> {}

/**
 * 通知模型类，继承自Sequelize的Model基类
 * 实现了通知的基本操作和关联关系
 */
export class Notification extends Model<NotificationAttributes, NotificationCreationAttributes> implements NotificationAttributes {
  public id!: number
  public type!: 'interaction' | 'content' | 'system' | 'marketing'
  public title!: string
  public content?: string
  public priority!: 'high' | 'medium' | 'low'
  public recipientId!: number
  public senderId?: number
  public relatedType?: 'article' | 'post' | 'comment' | 'user' | 'system'
  public relatedId?: number
  public actionUrl?: string
  public isRead!: boolean
  public readAt?: Date
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly recipient?: User
  public readonly sender?: User

  public static associations: {
    recipient: Association<Notification, User>
    sender: Association<Notification, User>
  }

  /**
   * 标记通知为已读
   */
  public async markAsRead(): Promise<void> {
    if (!this.isRead) {
      await this.update({
        isRead: true,
        readAt: new Date()
      })
    }
  }

  /**
   * 标记通知为未读
   */
  public async markAsUnread(): Promise<void> {
    if (this.isRead) {
      await this.update({
        isRead: false,
        readAt: null
      })
    }
  }

  /**
   * 获取通知的显示文本
   */
  public getDisplayText(): string {
    return this.content || this.title
  }

  /**
   * 检查通知是否过期（超过30天）
   */
  public isExpired(): boolean {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    return this.createdAt < thirtyDaysAgo
  }

  /**
   * 获取通知的优先级权重（用于排序）
   */
  public getPriorityWeight(): number {
    switch (this.priority) {
      case 'high': return 3
      case 'medium': return 2
      case 'low': return 1
      default: return 1
    }
  }

  /**
   * 创建互动类型通知的静态方法
   */
  public static async createInteractionNotification(data: {
    recipientId: number
    senderId: number
    title: string
    content?: string
    relatedType: 'article' | 'post' | 'comment'
    relatedId: number
    actionUrl?: string
  }): Promise<Notification> {
    return await Notification.create({
      type: 'interaction',
      priority: 'medium',
      ...data
    })
  }

  /**
   * 创建内容类型通知的静态方法
   */
  public static async createContentNotification(data: {
    recipientId: number
    senderId?: number
    title: string
    content?: string
    relatedType: 'article' | 'post'
    relatedId: number
    actionUrl?: string
  }): Promise<Notification> {
    return await Notification.create({
      type: 'content',
      priority: 'medium',
      ...data
    })
  }

  /**
   * 创建系统类型通知的静态方法
   */
  public static async createSystemNotification(data: {
    recipientId: number
    title: string
    content?: string
    priority?: 'high' | 'medium' | 'low'
    actionUrl?: string
  }): Promise<Notification> {
    return await Notification.create({
      type: 'system',
      priority: data.priority || 'high',
      relatedType: 'system',
      ...data
    })
  }

  /**
   * 批量创建通知的静态方法
   */
  public static async createBulkNotifications(notifications: NotificationCreationAttributes[]): Promise<Notification[]> {
    return await Notification.bulkCreate(notifications)
  }

  /**
   * 获取用户未读通知数量
   */
  public static async getUnreadCount(userId: number): Promise<number> {
    return await Notification.count({
      where: {
        recipientId: userId,
        isRead: false
      }
    })
  }

  /**
   * 批量标记通知为已读
   */
  public static async markBulkAsRead(notificationIds: number[], userId: number): Promise<number> {
    const [affectedCount] = await Notification.update(
      {
        isRead: true,
        readAt: new Date()
      },
      {
        where: {
          id: notificationIds,
          recipientId: userId,
          isRead: false
        }
      }
    )
    return affectedCount
  }

  /**
   * 清理过期通知
   */
  public static async cleanupExpiredNotifications(): Promise<number> {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    return await Notification.destroy({
      where: {
        createdAt: {
          [sequelize.Op.lt]: thirtyDaysAgo
        },
        isRead: true
      }
    })
  }
}

/**
 * 初始化通知模型的数据库映射配置
 */
Notification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
      allowNull: false,
      validate: {
        isIn: [['interaction', 'content', 'system', 'marketing']]
      }
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [1, 200],
        notEmpty: true
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    priority: {
      type: DataTypes.ENUM('high', 'medium', 'low'),
      allowNull: false,
      defaultValue: 'medium',
      validate: {
        isIn: [['high', 'medium', 'low']]
      }
    },
    recipientId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'recipient_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    senderId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'sender_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    relatedType: {
      type: DataTypes.ENUM('article', 'post', 'comment', 'user', 'system'),
      allowNull: true,
      field: 'related_type',
      validate: {
        isIn: [['article', 'post', 'comment', 'user', 'system']]
      }
    },
    relatedId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'related_id'
    },
    actionUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      field: 'action_url',
      validate: {
        len: [0, 500]
      }
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_read'
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'read_at'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'Notification',
    tableName: 'notifications',
    timestamps: true,
    indexes: [
      {
        name: 'idx_recipient_created',
        fields: ['recipient_id', 'created_at']
      },
      {
        name: 'idx_recipient_unread',
        fields: ['recipient_id', 'is_read']
      },
      {
        name: 'idx_type_priority',
        fields: ['type', 'priority']
      },
      {
        name: 'idx_related',
        fields: ['related_type', 'related_id']
      }
    ]
  }
)
