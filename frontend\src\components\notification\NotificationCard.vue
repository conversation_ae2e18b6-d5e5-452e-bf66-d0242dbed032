<template>
  <div 
    class="notification-card"
    :class="{
      'notification-card--unread': !notification.isRead,
      'notification-card--high': notification.priority === 'high',
      'notification-card--medium': notification.priority === 'medium',
      'notification-card--low': notification.priority === 'low'
    }"
    @click="handleClick"
  >
    <!-- 通知图标和类型 -->
    <div class="notification-card__icon">
      <el-icon :size="20" :color="getTypeColor(notification.type)">
        <component :is="getTypeIcon(notification.type)" />
      </el-icon>
    </div>

    <!-- 通知内容 -->
    <div class="notification-card__content">
      <!-- 标题和时间 -->
      <div class="notification-card__header">
        <h4 class="notification-card__title">{{ notification.title }}</h4>
        <div class="notification-card__meta">
          <span class="notification-card__time">{{ formatTime(notification.createdAt) }}</span>
          <el-tag 
            v-if="notification.priority === 'high'" 
            type="danger" 
            size="small"
            effect="plain"
          >
            高优先级
          </el-tag>
        </div>
      </div>

      <!-- 发送者信息 -->
      <div v-if="notification.sender" class="notification-card__sender">
        <el-icon size="14"><User /></el-icon>
        <span>{{ notification.sender.username }}</span>
      </div>

      <!-- 通知内容 -->
      <div v-if="notification.content" class="notification-card__text">
        {{ notification.content }}
      </div>

      <!-- 操作按钮 -->
      <div class="notification-card__actions">
        <el-button 
          v-if="!notification.isRead"
          type="primary" 
          size="small" 
          plain
          @click.stop="markAsRead"
          :loading="markingRead"
        >
          标记已读
        </el-button>
        
        <el-button 
          v-if="notification.actionUrl"
          type="success" 
          size="small" 
          plain
          @click.stop="handleAction"
        >
          查看详情
        </el-button>

        <el-button 
          type="danger" 
          size="small" 
          plain
          @click.stop="deleteNotification"
          :loading="deleting"
        >
          删除
        </el-button>
      </div>
    </div>

    <!-- 未读标识 -->
    <div v-if="!notification.isRead" class="notification-card__unread-dot"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ChatDotRound, 
  Document, 
  Setting, 
  Present, 
  User 
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import { NOTIFICATION_CONFIG } from '@/types/notification'
import type { Notification } from '@/types/notification'

interface Props {
  notification: Notification
}

const props = defineProps<Props>()

const router = useRouter()
const notificationStore = useNotificationStore()

const markingRead = ref(false)
const deleting = ref(false)

// 获取通知类型图标
const getTypeIcon = (type: string) => {
  const iconMap = {
    interaction: ChatDotRound,
    content: Document,
    system: Setting,
    marketing: Present
  }
  return iconMap[type as keyof typeof iconMap] || Document
}

// 获取通知类型颜色
const getTypeColor = (type: string) => {
  return NOTIFICATION_CONFIG.TYPES[type as keyof typeof NOTIFICATION_CONFIG.TYPES]?.color || '#409EFF'
}

// 格式化时间
const formatTime = (timeString: string) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 超过7天显示具体日期
  return time.toLocaleDateString()
}

// 点击通知卡片
const handleClick = () => {
  if (!props.notification.isRead) {
    markAsRead()
  }
}

// 标记为已读
const markAsRead = async () => {
  if (props.notification.isRead || markingRead.value) return
  
  try {
    markingRead.value = true
    await notificationStore.markAsRead(props.notification.id)
  } catch (error) {
    console.error('标记通知已读失败:', error)
  } finally {
    markingRead.value = false
  }
}

// 处理操作按钮点击
const handleAction = () => {
  if (props.notification.actionUrl) {
    // 如果是外部链接
    if (props.notification.actionUrl.startsWith('http')) {
      window.open(props.notification.actionUrl, '_blank')
    } else {
      // 内部路由
      router.push(props.notification.actionUrl)
    }
  }
}

// 删除通知
const deleteNotification = async () => {
  if (deleting.value) return

  try {
    deleting.value = true
    await notificationStore.deleteNotification(props.notification.id)
  } catch (error) {
    console.error('删除通知失败:', error)
  } finally {
    deleting.value = false
  }
}
</script>

<style scoped>
.notification-card {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notification-card--unread {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-7);
}

.notification-card--high {
  border-left: 4px solid var(--el-color-danger);
}

.notification-card--medium {
  border-left: 4px solid var(--el-color-warning);
}

.notification-card--low {
  border-left: 4px solid var(--el-color-info);
}

.notification-card__icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--el-color-primary-light-9);
  border-radius: 50%;
}

.notification-card__content {
  flex: 1;
  min-width: 0;
}

.notification-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notification-card__title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.4;
}

.notification-card__meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.notification-card__time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
}

.notification-card__sender {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.notification-card__text {
  margin-bottom: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  word-break: break-word;
}

.notification-card__actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.notification-card__unread-dot {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 8px;
  height: 8px;
  background: var(--el-color-primary);
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-card {
    padding: 12px;
    gap: 8px;
  }
  
  .notification-card__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .notification-card__title {
    font-size: 14px;
  }
  
  .notification-card__actions {
    gap: 6px;
  }
  
  .notification-card__actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }
}
</style>
