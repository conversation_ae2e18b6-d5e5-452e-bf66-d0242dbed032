import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useUIStore } from '@/stores/ui'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/blog/Home.vue')
    },
    {
      path: '/article/:id',
      name: 'ArticleDetail',
      component: () => import('@/views/blog/ArticleDetail.vue'),
      props: true
    },
    {
      path: '/article',
      name: 'ArticleList',
      component: () => import('@/views/blog/ArticleList.vue')
    },
    {
      path: '/tags',
      name: 'TagList',
      component: () => import('@/views/blog/TagList.vue')
    },
    {
      path: '/category',
      name: 'CategoryList',
      component: () => import('@/views/blog/CategoryList.vue')
    },
    {
      path: '/category/:slug',
      name: 'CategoryDetail',
      component: () => import('@/views/blog/CategoryDetail.vue'),
      props: true
    },
    {
      path: '/tag/:name',
      name: 'Tag',
      component: () => import('@/views/blog/TagDetail.vue')
    },
    {
      path: '/posts',
      name: 'Posts',
      component: () => import('@/views/blog/Posts.vue')
    },
    {
      path: '/posts/:id',
      name: 'PostDetail',
      component: () => import('@/views/blog/PostDetail.vue'),
      props: true
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue')
    },
    {
      path: '/design-system',
      name: 'DesignSystem',
      component: () => import('@/views/DesignSystem.vue')
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/admin/index.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/articles',
      name: 'AdminArticles',
      component: () => import('@/views/admin/Articles.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/articles/new',
      name: 'AdminArticleNew',
      component: () => import('@/views/admin/ArticleEdit.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/articles/:id/edit',
      name: 'AdminArticleEdit',
      component: () => import('@/views/admin/ArticleEdit.vue'),
      meta: { requiresAuth: true },
      props: true
    },
    {
      path: '/admin/tags',
      name: 'AdminTags',
      component: () => import('@/views/admin/Tags.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/categories',
      name: 'AdminCategories',
      component: () => import('@/views/admin/Categories.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/comments',
      name: 'AdminComments',
      component: () => import('@/views/admin/Comments.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/posts',
      name: 'AdminPosts',
      component: () => import('@/views/admin/Posts.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/media',
      name: 'AdminMedia',
      component: () => import('@/views/admin/MediaManagement.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/notifications',
      name: 'AdminNotifications',
      component: () => import('@/views/admin/NotificationCenter.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('@/views/Settings.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/roles',
      name: 'AdminRoles',
      component: () => import('@/views/admin/RoleManagement.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/user-roles',
      name: 'AdminUserRoles',
      component: () => import('@/views/admin/UserRoleAssignment.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFound.vue')
    }
  ]
})

// Navigation guard for authentication
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()
  const uiStore = useUIStore()

  try {
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      next('/login')
    } else {
      next()
    }
  } catch (error) {
    console.error('Navigation error:', error)
    uiStore.showError('页面导航失败，请重试')
    next(false)
  }
})

// Global error handler for route loading errors
router.onError((error) => {
  const uiStore = useUIStore()
  console.error('Router error:', error)
  uiStore.showError('页面加载失败，请刷新页面重试')
})

export default router