import { Router } from 'express'
import {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  deleteCategories,
  getCategoryArticles,
  getCategoryStats
} from '../controllers/category'
import { authenticateToken } from '../middleware/auth'
import { validateCategory, validateCategoryUpdate } from '../middleware/validation'
import { requirePermission } from '../middleware/permission'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: 分类的唯一标识符
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: 分类名称
 *         slug:
 *           type: string
 *           maxLength: 100
 *           description: URL友好的分类标识符
 *         description:
 *           type: string
 *           description: 分类描述
 *         parentId:
 *           type: integer
 *           nullable: true
 *           description: 父分类ID
 *         sort:
 *           type: integer
 *           description: 排序值
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *         parent:
 *           $ref: '#/components/schemas/Category'
 *           description: 父分类信息
 *         children:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Category'
 *           description: 子分类列表
 *       example:
 *         id: 1
 *         name: "技术"
 *         slug: "tech"
 *         description: "技术相关文章分类"
 *         parentId: null
 *         sort: 0
 *         createdAt: "2023-01-01T00:00:00.000Z"
 *         updatedAt: "2023-01-01T00:00:00.000Z"
 *
 *     CategoryInput:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: 分类名称
 *         slug:
 *           type: string
 *           maxLength: 100
 *           description: URL友好的分类标识符（可选，系统会自动生成）
 *         description:
 *           type: string
 *           description: 分类描述
 *         parentId:
 *           type: integer
 *           nullable: true
 *           description: 父分类ID
 *         sort:
 *           type: integer
 *           default: 0
 *           description: 排序值
 *       example:
 *         name: "前端开发"
 *         description: "前端开发相关技术文章"
 *         parentId: 1
 *         sort: 1
 */

/**
 * @swagger
 * /api/categories:
 *   get:
 *     summary: 获取分类列表
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: tree
 *         schema:
 *           type: boolean
 *         description: 是否返回树结构
 *       - in: query
 *         name: flat
 *         schema:
 *           type: boolean
 *         description: 是否返回扁平列表（包含层级信息）
 *       - in: query
 *         name: stats
 *         schema:
 *           type: boolean
 *         description: 是否包含统计信息
 *     responses:
 *       200:
 *         description: 成功获取分类列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Category'
 */
router.get('/', getCategories)

/**
 * @swagger
 * /api/categories/stats:
 *   get:
 *     summary: 获取分类统计信息
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: includeChildren
 *         schema:
 *           type: boolean
 *           default: true
 *         description: 是否包含子分类的文章数量
 *     responses:
 *       200:
 *         description: 成功获取分类统计信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           category:
 *                             $ref: '#/components/schemas/Category'
 *                           articleCount:
 *                             type: integer
 */
router.get('/stats', getCategoryStats)

/**
 * @swagger
 * /api/categories/{id}:
 *   get:
 *     summary: 根据ID或slug获取单个分类
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 分类ID或slug
 *     responses:
 *       200:
 *         description: 成功获取分类信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     category:
 *                       $ref: '#/components/schemas/Category'
 *       404:
 *         description: 分类不存在
 */
router.get('/:id', getCategory)

/**
 * @swagger
 * /api/categories/{id}/articles:
 *   get:
 *     summary: 获取分类下的文章列表
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 分类ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, published]
 *           default: published
 *         description: 文章状态
 *       - in: query
 *         name: includeChildren
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含子分类的文章
 *     responses:
 *       200:
 *         description: 成功获取分类文章列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     category:
 *                       $ref: '#/components/schemas/Category'
 *                     articles:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Article'
 *                     pagination:
 *                       type: object
 *       404:
 *         description: 分类不存在
 */
router.get('/:id/articles', getCategoryArticles)

/**
 * @swagger
 * /api/categories:
 *   post:
 *     summary: 创建新分类
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CategoryInput'
 *     responses:
 *       201:
 *         description: 分类创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     category:
 *                       $ref: '#/components/schemas/Category'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post('/', authenticateToken, requirePermission('category.create'), validateCategory, createCategory)

/**
 * @swagger
 * /api/categories/{id}:
 *   put:
 *     summary: 更新分类
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 分类ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CategoryInput'
 *     responses:
 *       200:
 *         description: 分类更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     category:
 *                       $ref: '#/components/schemas/Category'
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分类不存在
 */
router.put('/:id', authenticateToken, requirePermission('category.update'), validateCategoryUpdate, updateCategory)

/**
 * @swagger
 * /api/categories/{id}:
 *   delete:
 *     summary: 删除分类
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 分类ID
 *     responses:
 *       200:
 *         description: 分类删除成功
 *       400:
 *         description: 分类包含子分类或文章，无法删除
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分类不存在
 */
router.delete('/:id', authenticateToken, requirePermission('category.delete'), deleteCategory)

/**
 * @swagger
 * /api/categories/batch:
 *   delete:
 *     summary: 批量删除分类
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 要删除的分类ID数组
 *     responses:
 *       200:
 *         description: 分类批量删除成功
 *       400:
 *         description: 请求参数错误或分类包含子分类/文章
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分类不存在
 */
router.delete('/batch', authenticateToken, requirePermission('category.delete'), deleteCategories)

export default router
