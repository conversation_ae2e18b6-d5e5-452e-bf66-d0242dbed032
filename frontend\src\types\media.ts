/**
 * 媒体文件相关的TypeScript类型定义
 */

/**
 * 媒体文件类别枚举
 */
export type MediaCategory = 'image' | 'video' | 'audio' | 'document'

/**
 * 媒体文件排序字段
 */
export type MediaSortBy = 'createdAt' | 'size' | 'originalName'

/**
 * 排序方向
 */
export type SortOrder = 'ASC' | 'DESC'

/**
 * 媒体文件基础信息接口
 */
export interface MediaAttributes {
  id: number
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  width?: number
  height?: number
  uploaderId: number
  category: MediaCategory
  tags?: string[]
  description?: string
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

/**
 * 媒体文件创建时的属性接口
 */
export interface MediaCreationAttributes {
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  width?: number
  height?: number
  uploaderId: number
  category: MediaCategory
  tags?: string[]
  description?: string
  isPublic?: boolean
}

/**
 * 媒体文件更新时的属性接口
 */
export interface MediaUpdateAttributes {
  description?: string
  tags?: string[]
  isPublic?: boolean
}

/**
 * 上传者信息接口
 */
export interface MediaUploader {
  id: number
  username: string
  email?: string
}

/**
 * 完整的媒体文件信息接口（包含关联数据）
 */
export interface MediaWithUploader extends MediaAttributes {
  uploader: MediaUploader
}

/**
 * 媒体文件查询参数接口
 */
export interface MediaQueryParams {
  page?: number
  limit?: number
  category?: MediaCategory
  search?: string
  uploaderId?: number
  isPublic?: boolean
  sortBy?: MediaSortBy
  sortOrder?: SortOrder
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

/**
 * 媒体列表响应接口
 */
export interface MediaListResponse {
  media: MediaWithUploader[]
  pagination: PaginationInfo
}

/**
 * 媒体统计信息接口
 */
export interface MediaStats {
  total: number
  byCategory: {
    image: number
    video: number
    audio: number
    document: number
  }
  totalSize: number
  formattedTotalSize: string
}

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

/**
 * 媒体列表API响应接口
 */
export interface MediaListApiResponse extends ApiResponse<MediaListResponse> {}

/**
 * 媒体详情API响应接口
 */
export interface MediaDetailApiResponse extends ApiResponse<MediaWithUploader> {}

/**
 * 媒体统计API响应接口
 */
export interface MediaStatsApiResponse extends ApiResponse<MediaStats> {}

/**
 * 文件上传响应接口
 */
export interface UploadResponse {
  id?: number
  filename: string
  originalName: string
  size: number
  url: string
  mimeType: string
  category: MediaCategory
}

/**
 * 单文件上传API响应接口
 */
export interface SingleUploadApiResponse extends ApiResponse<UploadResponse> {}

/**
 * 多文件上传响应接口
 */
export interface MultipleUploadResponse {
  files: UploadResponse[]
  count: number
  mediaRecords: number
}

/**
 * 多文件上传API响应接口
 */
export interface MultipleUploadApiResponse extends ApiResponse<MultipleUploadResponse> {}

/**
 * 媒体文件过滤器接口
 */
export interface MediaFilter {
  category?: MediaCategory
  search?: string
  uploaderId?: number
  isPublic?: boolean
  dateRange?: {
    start: string
    end: string
  }
  sizeRange?: {
    min: number
    max: number
  }
}

/**
 * 媒体文件批量操作接口
 */
export interface MediaBatchOperation {
  action: 'delete' | 'updateTags' | 'updateVisibility'
  mediaIds: number[]
  data?: {
    tags?: string[]
    isPublic?: boolean
  }
}

/**
 * 媒体文件预览信息接口
 */
export interface MediaPreview {
  id: number
  url: string
  thumbnailUrl?: string
  originalName: string
  mimeType: string
  category: MediaCategory
  width?: number
  height?: number
}

/**
 * 媒体文件选择器配置接口
 */
export interface MediaSelectorConfig {
  multiple?: boolean
  accept?: MediaCategory[]
  maxSize?: number
  maxCount?: number
  showUpload?: boolean
  showPreview?: boolean
}

/**
 * 媒体文件上传配置接口
 */
export interface MediaUploadConfig {
  maxFileSize: number
  maxFiles: number
  allowedTypes: string[]
  uploadUrl: string
}

/**
 * 媒体文件工具函数类型
 */
export interface MediaUtils {
  formatFileSize: (bytes: number) => string
  getFileExtension: (filename: string) => string
  isImage: (mimeType: string) => boolean
  isVideo: (mimeType: string) => boolean
  isAudio: (mimeType: string) => boolean
  getCategoryFromMimeType: (mimeType: string) => MediaCategory
  validateFileType: (mimeType: string) => boolean
  generateThumbnail: (file: File) => Promise<string>
}

/**
 * 媒体文件错误类型
 */
export interface MediaError {
  code: string
  message: string
  details?: any
}

/**
 * 媒体文件上传进度接口
 */
export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
  speed?: number
  timeRemaining?: number
}
