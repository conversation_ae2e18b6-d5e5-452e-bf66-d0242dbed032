import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 创建RBAC（基于角色的访问控制）系统相关数据表的迁移文件
 * 包括：roles（角色表）、permissions（权限表）、user_roles（用户角色关联表）、role_permissions（角色权限关联表）
 */

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建角色表
  await queryInterface.createTable('roles', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建权限表
  await queryInterface.createTable('permissions', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    resource: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建用户角色关联表
  await queryInterface.createTable('user_roles', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建角色权限关联表
  await queryInterface.createTable('role_permissions', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    permission_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'permissions',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建索引
  // 角色表索引
  await queryInterface.addIndex('roles', ['name'], { unique: true })
  await queryInterface.addIndex('roles', ['is_active'])
  await queryInterface.addIndex('roles', ['is_system'])

  // 权限表索引
  await queryInterface.addIndex('permissions', ['name'], { unique: true })
  await queryInterface.addIndex('permissions', ['resource', 'action'], { unique: true })
  await queryInterface.addIndex('permissions', ['resource'])
  await queryInterface.addIndex('permissions', ['action'])
  await queryInterface.addIndex('permissions', ['is_active'])

  // 用户角色关联表索引
  await queryInterface.addIndex('user_roles', ['user_id', 'role_id'], { unique: true })
  await queryInterface.addIndex('user_roles', ['user_id'])
  await queryInterface.addIndex('user_roles', ['role_id'])
  await queryInterface.addIndex('user_roles', ['assigned_by'])
  await queryInterface.addIndex('user_roles', ['assigned_at'])

  // 角色权限关联表索引
  await queryInterface.addIndex('role_permissions', ['role_id', 'permission_id'], { unique: true })
  await queryInterface.addIndex('role_permissions', ['role_id'])
  await queryInterface.addIndex('role_permissions', ['permission_id'])
  await queryInterface.addIndex('role_permissions', ['assigned_by'])
  await queryInterface.addIndex('role_permissions', ['assigned_at'])
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 删除表（按依赖关系的逆序）
  await queryInterface.dropTable('role_permissions')
  await queryInterface.dropTable('user_roles')
  await queryInterface.dropTable('permissions')
  await queryInterface.dropTable('roles')
}
