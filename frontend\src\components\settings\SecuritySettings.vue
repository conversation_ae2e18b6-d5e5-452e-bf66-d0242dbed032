<template>
  <div class="security-settings">
    <div class="settings-section-header">
      <h3 class="section-title">
        <el-icon><Shield /></el-icon>
        安全设置
      </h3>
      <p class="section-description">保护您的账户安全</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      label-width="140px"
      class="security-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 两步验证 -->
      <el-form-item label="两步验证">
        <div class="security-item">
          <div class="security-content">
            <div class="security-header">
              <el-switch
                v-model="formData.twoFactorEnabled"
                size="large"
                :active-icon="Shield"
                :inactive-icon="Lock"
                @change="handleTwoFactorChange"
              />
              <div class="security-info">
                <div class="security-title">
                  两步验证
                  <el-tag 
                    v-if="formData.twoFactorEnabled" 
                    type="success" 
                    size="small"
                    effect="light"
                  >
                    已启用
                  </el-tag>
                  <el-tag 
                    v-else 
                    type="warning" 
                    size="small"
                    effect="light"
                  >
                    未启用
                  </el-tag>
                </div>
                <div class="security-desc">
                  为您的账户添加额外的安全保护层，登录时需要验证码
                </div>
              </div>
            </div>
            
            <!-- 两步验证详情 -->
            <div v-if="formData.twoFactorEnabled" class="two-factor-details">
              <el-alert
                title="两步验证已启用"
                type="success"
                show-icon
                :closable="false"
              >
                <template #default>
                  <p>您的账户已启用两步验证保护。登录时需要输入验证码。</p>
                  <div class="two-factor-actions">
                    <el-button size="small" type="primary" plain>
                      <el-icon><View /></el-icon>
                      查看备用码
                    </el-button>
                    <el-button size="small" type="warning" plain>
                      <el-icon><Refresh /></el-icon>
                      重新生成备用码
                    </el-button>
                  </div>
                </template>
              </el-alert>
            </div>
            
            <div v-else class="two-factor-setup">
              <el-alert
                title="建议启用两步验证"
                type="warning"
                show-icon
                :closable="false"
              >
                <template #default>
                  <p>两步验证可以大大提高您账户的安全性，建议立即启用。</p>
                  <div class="setup-steps">
                    <div class="step-item">
                      <el-icon class="step-icon"><Download /></el-icon>
                      <span>下载验证器应用（如 Google Authenticator）</span>
                    </div>
                    <div class="step-item">
                      <el-icon class="step-icon"><QrCode /></el-icon>
                      <span>扫描二维码或输入密钥</span>
                    </div>
                    <div class="step-item">
                      <el-icon class="step-icon"><Key /></el-icon>
                      <span>输入验证码完成设置</span>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 密码安全 -->
      <el-form-item label="密码安全">
        <div class="security-item">
          <div class="password-security">
            <div class="password-info">
              <div class="password-header">
                <el-icon class="password-icon"><Key /></el-icon>
                <div class="password-details">
                  <div class="password-title">密码强度</div>
                  <div class="password-desc">上次修改：2024年12月29日</div>
                </div>
              </div>
              <div class="password-strength">
                <el-progress
                  :percentage="passwordStrength"
                  :color="passwordStrengthColor"
                  :stroke-width="8"
                  text-inside
                  :format="formatPasswordStrength"
                />
              </div>
            </div>
            <div class="password-actions">
              <el-button type="primary" plain>
                <el-icon><Edit /></el-icon>
                修改密码
              </el-button>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 登录活动 -->
      <el-form-item label="登录活动">
        <div class="security-item">
          <div class="login-activity">
            <div class="activity-header">
              <el-icon class="activity-icon"><Monitor /></el-icon>
              <div class="activity-info">
                <div class="activity-title">最近登录活动</div>
                <div class="activity-desc">查看您账户的登录记录</div>
              </div>
            </div>
            <div class="activity-list">
              <div class="activity-item">
                <div class="activity-details">
                  <div class="activity-device">
                    <el-icon><Monitor /></el-icon>
                    <span>Windows 11 - Chrome</span>
                  </div>
                  <div class="activity-location">北京, 中国</div>
                  <div class="activity-time">2024年12月29日 14:30</div>
                </div>
                <el-tag type="success" size="small">当前会话</el-tag>
              </div>
              <div class="activity-item">
                <div class="activity-details">
                  <div class="activity-device">
                    <el-icon><Iphone /></el-icon>
                    <span>iPhone - Safari</span>
                  </div>
                  <div class="activity-location">上海, 中国</div>
                  <div class="activity-time">2024年12月28日 09:15</div>
                </div>
                <el-button size="small" type="danger" plain>
                  终止会话
                </el-button>
              </div>
            </div>
            <div class="activity-actions">
              <el-button type="primary" plain>
                <el-icon><View /></el-icon>
                查看完整记录
              </el-button>
              <el-button type="danger" plain>
                <el-icon><SwitchButton /></el-icon>
                终止所有其他会话
              </el-button>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            保存安全设置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { 
  Shield, 
  Lock, 
  View, 
  Refresh, 
  Download, 
  QrCode, 
  Key, 
  Edit, 
  Monitor, 
  Iphone, 
  SwitchButton, 
  Check 
} from '@element-plus/icons-vue'

import type { UserSettings, SettingsUpdateParams } from '@/services/settings'

// ==================== Props & Emits ====================

interface Props {
  settings: UserSettings | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  update: [data: SettingsUpdateParams]
}>()

// ==================== 响应式数据 ====================

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  twoFactorEnabled: false
})

// ==================== 计算属性 ====================

// 密码强度（模拟数据）
const passwordStrength = computed(() => 75)

// 密码强度颜色
const passwordStrengthColor = computed(() => {
  const strength = passwordStrength.value
  if (strength >= 80) return '#67c23a'
  if (strength >= 60) return '#e6a23c'
  return '#f56c6c'
})

// ==================== 监听器 ====================

// 监听设置数据变化，更新表单
watch(
  () => props.settings,
  (newSettings) => {
    if (newSettings) {
      formData.twoFactorEnabled = newSettings.twoFactorEnabled ?? false
    }
  },
  { immediate: true }
)

// ==================== 方法 ====================

/**
 * 格式化密码强度显示
 */
const formatPasswordStrength = (percentage: number): string => {
  if (percentage >= 80) return '强'
  if (percentage >= 60) return '中'
  return '弱'
}

/**
 * 处理两步验证开关变化
 */
const handleTwoFactorChange = async (value: boolean) => {
  if (value) {
    // 启用两步验证
    try {
      await ElMessageBox.confirm(
        '启用两步验证需要您下载验证器应用并完成设置。确定要继续吗？',
        '启用两步验证',
        {
          confirmButtonText: '继续设置',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      // 这里应该打开两步验证设置流程
      ElMessage.info('请按照指引完成两步验证设置')
    } catch (error) {
      // 用户取消，恢复开关状态
      formData.twoFactorEnabled = false
    }
  } else {
    // 禁用两步验证
    try {
      await ElMessageBox.confirm(
        '禁用两步验证会降低您账户的安全性。确定要禁用吗？',
        '禁用两步验证',
        {
          confirmButtonText: '确定禁用',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      ElMessage.success('两步验证已禁用')
    } catch (error) {
      // 用户取消，恢复开关状态
      formData.twoFactorEnabled = true
    }
  }
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  // 准备更新数据
  const updateData: SettingsUpdateParams = {
    twoFactorEnabled: formData.twoFactorEnabled
  }

  emit('update', updateData)
}

/**
 * 重置表单
 */
const handleReset = () => {
  if (props.settings) {
    formData.twoFactorEnabled = props.settings.twoFactorEnabled ?? false
  }
  
  ElMessage.success('表单已重置')
}
</script>

<style scoped>
.security-settings {
  max-width: 700px;
}

.settings-section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.security-form {
  margin-top: 24px;
}

.security-item {
  width: 100%;
}

.security-content {
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
}

.security-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.security-info {
  flex: 1;
}

.security-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.security-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.two-factor-details,
.two-factor-setup {
  margin-top: 16px;
}

.two-factor-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.setup-steps {
  margin-top: 12px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.step-icon {
  color: var(--el-color-primary);
}

.password-security {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
}

.password-info {
  flex: 1;
  margin-right: 20px;
}

.password-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.password-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.password-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.password-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.password-strength {
  max-width: 200px;
}

.login-activity {
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
}

.activity-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.activity-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.activity-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.activity-desc {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.activity-list {
  margin-bottom: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.activity-details {
  flex: 1;
}

.activity-device {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.activity-location,
.activity-time {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.activity-actions {
  display: flex;
  gap: 12px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .security-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .password-security {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .password-info {
    margin-right: 0;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .activity-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
