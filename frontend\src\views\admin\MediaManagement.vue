<template>
  <div class="media-management">
    <!-- 页面头部 -->
    <header class="admin-header">
      <h1>媒体管理</h1>
      <div class="user-info">
        <span v-if="authStore.user">欢迎, {{ authStore.user.username }}</span>
        <button @click="handleLogout" class="logout-button">退出登录</button>
      </div>
    </header>

    <!-- 导航栏 -->
    <nav class="admin-nav">
      <router-link to="/admin" class="nav-link">仪表板</router-link>
      <router-link to="/admin/articles" class="nav-link">文章管理</router-link>
      <router-link to="/admin/posts" class="nav-link">说说管理</router-link>
      <router-link to="/admin/media" class="nav-link">媒体管理</router-link>
      <router-link to="/admin/categories" class="nav-link">分类管理</router-link>
      <router-link to="/admin/tags" class="nav-link">标签管理</router-link>
      <router-link to="/admin/comments" class="nav-link">评论管理</router-link>
      <router-link to="/admin/notifications" class="nav-link">通知中心</router-link>
      <router-link to="/admin/roles" class="nav-link">角色管理</router-link>
      <router-link to="/admin/user-roles" class="nav-link">用户角色</router-link>
      <router-link to="/settings" class="nav-link">设置</router-link>
    </nav>

    <!-- 页面内容头部 -->
    <div class="media-management__header">
      <div class="media-management__title">
        <h2>媒体文件</h2>
        <p>管理所有上传的媒体文件，包括图片、视频、音频和文档</p>
      </div>
      
      <div class="media-management__actions">
        <el-button
          type="primary"
          @click="showUploadDialog = true"
        >
          <el-icon><Plus /></el-icon>
          上传文件
        </el-button>
        
        <el-button
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="media-management__stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-card__content">
              <div class="stats-card__icon stats-card__icon--total">
                <el-icon><Files /></el-icon>
              </div>
              <div class="stats-card__info">
                <div class="stats-card__value">{{ formattedStats.total }}</div>
                <div class="stats-card__label">总文件数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-card__content">
              <div class="stats-card__icon stats-card__icon--image">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stats-card__info">
                <div class="stats-card__value">{{ formattedStats.byCategory.image }}</div>
                <div class="stats-card__label">图片</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-card__content">
              <div class="stats-card__icon stats-card__icon--video">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stats-card__info">
                <div class="stats-card__value">{{ formattedStats.byCategory.video }}</div>
                <div class="stats-card__label">视频</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-card__content">
              <div class="stats-card__icon stats-card__icon--size">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stats-card__info">
                <div class="stats-card__value">{{ formattedStats.formattedTotalSize }}</div>
                <div class="stats-card__label">总大小</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 媒体画廊 -->
    <div class="media-management__gallery">
      <el-card>
        <MediaGallery
          :selectable="true"
          :multiple="true"
          @select="handleMediaSelect"
          @preview="handleMediaPreview"
          @edit="handleMediaEdit"
          @delete="handleMediaDelete"
        />
      </el-card>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传媒体文件"
      width="800px"
      :close-on-click-modal="false"
    >
      <MediaUploader
        :multiple="true"
        :max-files="9"
        :auto-upload="false"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
    </el-dialog>

    <!-- 媒体预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="currentMedia?.originalName"
      width="80%"
      :close-on-click-modal="true"
    >
      <div v-if="currentMedia" class="media-preview">
        <!-- 图片预览 -->
        <div v-if="currentMedia.category === 'image'" class="media-preview__image">
          <el-image
            :src="currentMedia.url"
            :alt="currentMedia.originalName"
            fit="contain"
            style="max-width: 100%; max-height: 60vh;"
          />
        </div>
        
        <!-- 视频预览 -->
        <div v-else-if="currentMedia.category === 'video'" class="media-preview__video">
          <video
            :src="currentMedia.url"
            controls
            style="max-width: 100%; max-height: 60vh;"
          />
        </div>
        
        <!-- 音频预览 -->
        <div v-else-if="currentMedia.category === 'audio'" class="media-preview__audio">
          <audio :src="currentMedia.url" controls style="width: 100%;" />
        </div>
        
        <!-- 文档预览 -->
        <div v-else class="media-preview__document">
          <div class="media-preview__document-info">
            <el-icon><Document /></el-icon>
            <span>{{ currentMedia.originalName }}</span>
            <el-button type="primary" @click="downloadFile(currentMedia)">
              下载文件
            </el-button>
          </div>
        </div>
        
        <!-- 媒体信息 -->
        <div class="media-preview__info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">
              {{ currentMedia.originalName }}
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(currentMedia.size) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件类型">
              {{ currentMedia.mimeType }}
            </el-descriptions-item>
            <el-descriptions-item label="类别">
              {{ getCategoryLabel(currentMedia.category) }}
            </el-descriptions-item>
            <el-descriptions-item v-if="currentMedia.width && currentMedia.height" label="尺寸">
              {{ currentMedia.width }} × {{ currentMedia.height }}
            </el-descriptions-item>
            <el-descriptions-item label="上传者">
              {{ currentMedia.uploader.username }}
            </el-descriptions-item>
            <el-descriptions-item label="上传时间">
              {{ formatDateTime(currentMedia.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="可见性">
              <el-tag :type="currentMedia.isPublic ? 'success' : 'warning'">
                {{ currentMedia.isPublic ? '公开' : '私有' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="currentMedia.tags && currentMedia.tags.length > 0" label="标签" :span="2">
              <el-tag
                v-for="tag in currentMedia.tags"
                :key="tag"
                style="margin-right: 8px;"
              >
                {{ tag }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="currentMedia.description" label="描述" :span="2">
              {{ currentMedia.description }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 媒体编辑对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑媒体信息"
      width="600px"
    >
      <el-form
        v-if="currentMedia"
        ref="editFormRef"
        :model="editForm"
        label-width="80px"
      >
        <el-form-item label="描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入媒体描述"
          />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="editForm.tags"
            multiple
            filterable
            allow-create
            placeholder="输入或选择标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="可见性">
          <el-radio-group v-model="editForm.isPublic">
            <el-radio :label="true">公开</el-radio>
            <el-radio :label="false">私有</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSaveEdit"
          :loading="saving"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Refresh,
  Files,
  Picture,
  VideoPlay,
  FolderOpened,
  Document
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useMediaStore } from '@/stores/media'
import { MediaUtils } from '@/services/media'
import MediaGallery from '@/components/media/MediaGallery.vue'
import MediaUploader from '@/components/media/MediaUploader.vue'
import type { MediaWithUploader, MediaCategory, MediaUpdateAttributes } from '@/types/media'

// ==================== 响应式数据 ====================
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const showEditDialog = ref(false)
const currentMedia = ref<MediaWithUploader | null>(null)
const selectedMedia = ref<MediaWithUploader[]>([])
const saving = ref(false)

// 编辑表单
const editForm = ref<MediaUpdateAttributes>({
  description: '',
  tags: [],
  isPublic: true
})

// ==================== Store ====================
const router = useRouter()
const authStore = useAuthStore()
const mediaStore = useMediaStore()

// ==================== 计算属性 ====================
const {
  loading,
  formattedStats
} = mediaStore

const commonTags = computed(() => {
  // 从现有媒体中提取常用标签
  const tagCounts: Record<string, number> = {}
  mediaStore.mediaList.forEach(media => {
    media.tags?.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1
    })
  })
  
  return Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 20)
    .map(([tag]) => tag)
})

// ==================== 方法 ====================
/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  return MediaUtils.formatFileSize(bytes)
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN')
}

/**
 * 获取类别标签
 */
const getCategoryLabel = (category: MediaCategory): string => {
  const labels = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档'
  }
  return labels[category]
}

/**
 * 处理用户登出
 */
const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

/**
 * 刷新数据
 */
const refreshData = async () => {
  await Promise.all([
    mediaStore.fetchMediaList({ page: 1 }),
    mediaStore.fetchMediaStats()
  ])
}

/**
 * 处理媒体选择
 */
const handleMediaSelect = (media: MediaWithUploader[]) => {
  selectedMedia.value = media
}

/**
 * 处理媒体预览
 */
const handleMediaPreview = (media: MediaWithUploader) => {
  currentMedia.value = media
  showPreviewDialog.value = true
}

/**
 * 处理媒体编辑
 */
const handleMediaEdit = (media: MediaWithUploader) => {
  currentMedia.value = media
  editForm.value = {
    description: media.description || '',
    tags: media.tags || [],
    isPublic: media.isPublic
  }
  showEditDialog.value = true
}

/**
 * 处理媒体删除
 */
const handleMediaDelete = (media: MediaWithUploader) => {
  // 删除后刷新统计信息
  mediaStore.fetchMediaStats()
}

/**
 * 处理上传成功
 */
const handleUploadSuccess = (files: any[]) => {
  ElMessage.success(`成功上传 ${files.length} 个文件`)
  showUploadDialog.value = false
  
  // 刷新数据
  refreshData()
}

/**
 * 处理上传错误
 */
const handleUploadError = (error: Error) => {
  ElMessage.error(`上传失败: ${error.message}`)
}

/**
 * 保存编辑
 */
const handleSaveEdit = async () => {
  if (!currentMedia.value) return
  
  try {
    saving.value = true
    await mediaStore.updateMedia(currentMedia.value.id, editForm.value)
    ElMessage.success('更新成功')
    showEditDialog.value = false
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    saving.value = false
  }
}

/**
 * 下载文件
 */
const downloadFile = (media: MediaWithUploader) => {
  const link = document.createElement('a')
  link.href = media.url
  link.download = media.originalName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 初始化
 */
const initialize = async () => {
  await Promise.all([
    mediaStore.fetchMediaList(),
    mediaStore.fetchMediaStats()
  ])
}

// ==================== 生命周期 ====================
onMounted(() => {
  initialize()
})
</script>

<style scoped lang="scss">
.media-management {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);

  .admin-header {
    background: var(--el-bg-color);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--el-border-color-light);
    display: flex;
    justify-content: space-between;
    align-items: center;

    h1 {
      color: var(--el-text-color-primary);
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }

  .logout-button {
    padding: 8px 16px;
    background-color: var(--el-color-danger);
    color: white;
    border: none;
    border-radius: var(--el-border-radius-base);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;

    &:hover {
      background-color: var(--el-color-danger-dark-2);
    }
  }

  .admin-nav {
    background: var(--el-bg-color);
    padding: 0 2rem;
    border-bottom: 1px solid var(--el-border-color-light);
    display: flex;
    gap: 2rem;
  }

  .nav-link {
    padding: 1rem 0;
    text-decoration: none;
    color: var(--el-text-color-regular);
    border-bottom: 2px solid transparent;
    transition: color 0.2s, border-color 0.2s;

    &:hover,
    &.router-link-active {
      color: var(--el-color-primary);
      border-bottom-color: var(--el-color-primary);
    }
  }

  padding: 20px;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
  }
  
  &__title {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
  
  &__actions {
    display: flex;
    gap: 12px;
  }
  
  &__stats {
    margin-bottom: 24px;
  }
  
  &__gallery {
    .el-card {
      border-radius: 8px;
    }
  }
}

.stats-card {
  border-radius: 8px;
  
  &__content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  &__icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    
    &--total {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &--image {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &--video {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &--size {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }
  
  &__info {
    flex: 1;
  }
  
  &__value {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }
  
  &__label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.media-preview {
  &__image,
  &__video,
  &__audio {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
  
  &__document {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
  
  &__document-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 40px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
    
    .el-icon {
      font-size: 48px;
      color: var(--el-text-color-secondary);
    }
    
    span {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
  
  &__info {
    .el-descriptions {
      :deep(.el-descriptions__label) {
        font-weight: 500;
      }
    }
  }
}
</style>
