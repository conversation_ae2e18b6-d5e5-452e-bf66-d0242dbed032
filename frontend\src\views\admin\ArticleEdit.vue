<template>
  <div class="admin-article-editor-view">
    <header class="admin-header">
      <h1>文章管理</h1>
      <div class="user-info">
        <span v-if="authStore.user">欢迎, {{ authStore.user.username }}</span>
        <button @click="handleLogout" class="logout-button">退出登录</button>
      </div>
    </header>
    
    <nav class="admin-nav">
      <router-link to="/admin" class="nav-link">仪表板</router-link>
      <router-link to="/admin/articles" class="nav-link">文章管理</router-link>
      <router-link to="/admin/posts" class="nav-link">说说管理</router-link>
      <router-link to="/admin/media" class="nav-link">媒体管理</router-link>
      <router-link to="/admin/categories" class="nav-link">分类管理</router-link>
      <router-link to="/admin/tags" class="nav-link">标签管理</router-link>
      <router-link to="/admin/comments" class="nav-link">评论管理</router-link>
    </nav>
    
    <main class="admin-content">
      <ArticleEditor :article-id="articleId" />
    </main>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import ArticleEditor from '@/components/admin/ArticleEditor.vue'

interface Props {
  id?: string
}

const props = defineProps<Props>()
const router = useRouter()
const authStore = useAuthStore()

const articleId = props.id ? parseInt(props.id) : undefined

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

// Check authentication on mount
onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }
  
  // Verify token is still valid
  const isValid = await authStore.checkAuth()
  if (!isValid) {
    router.push('/login')
  }
})
</script>

<style scoped>
.admin-article-editor-view {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.admin-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-header h1 {
  color: #333;
  margin: 0;
  font-size: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info span {
  color: #666;
  font-size: 14px;
}

.logout-button {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: #c82333;
}

.admin-nav {
  background: white;
  padding: 0 2rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  gap: 2rem;
}

.nav-link {
  padding: 1rem 0;
  text-decoration: none;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: color 0.2s, border-color 0.2s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.admin-content {
  padding: 0;
}
</style>