import { Router } from 'express'
import authRoutes from './auth'
import articleRoutes from './article'
import tagRoutes from './tag'
import categoryRoutes from './category'
import commentRoutes from './comment'
import postRoutes from './post'
import mediaRoutes from './media'
import notificationRoutes from './notification'
import settingsRoutes from './settings'
import roleRoutes from './roles'
import permissionRoutes from './permissions'
import userRoleRoutes from './userRoles'

import timelineRoutes from './timeline'
import uploadRoutes from './upload'

const router = Router()

// 挂载认证相关路由到 /auth 路径
router.use('/auth', authRoutes)

// 挂载文章相关路由到 /articles 路径
router.use('/articles', articleRoutes)

// 挂载标签相关路由到 /tags 路径
router.use('/tags', tagRoutes)

// 挂载分类相关路由到 /categories 路径
router.use('/categories', categoryRoutes)

// 挂载评论相关路由到 /comments 路径
router.use('/comments', commentRoutes)

// 挂载说说相关路由到 /posts 路径
router.use('/posts', postRoutes)

// 挂载媒体相关路由到 /media 路径
router.use('/media', mediaRoutes)

// 挂载通知相关路由到 /notifications 路径
router.use('/notifications', notificationRoutes)

// 挂载设置相关路由到 /settings 路径
router.use('/settings', settingsRoutes)

// 挂载角色管理相关路由到 /roles 路径
router.use('/roles', roleRoutes)

// 挂载权限管理相关路由到 /permissions 路径
router.use('/permissions', permissionRoutes)

// 挂载用户角色管理相关路由到 /user-roles 路径
router.use('/user-roles', userRoleRoutes)

// 挂载时间线相关路由到 /timeline 路径
router.use('/timeline', timelineRoutes)

// 挂载上传相关路由到 /upload 路径
router.use('/upload', uploadRoutes)

/**
 * 处理根路径的GET请求，返回API基本信息和可用端点
 * @param req - Express请求对象
 * @param res - Express响应对象
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Personal Blog API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      articles: '/api/articles',
      tags: '/api/tags',
      categories: '/api/categories',
      comments: '/api/comments',
      posts: '/api/posts',
      media: '/api/media',
      notifications: '/api/notifications',
      settings: '/api/settings',
      roles: '/api/roles',
      permissions: '/api/permissions',
      userRoles: '/api/user-roles',
      timeline: '/api/timeline',
      upload: '/api/upload',
      health: '/health',
      docs: '/api-docs'
    }
  })
})

export default router