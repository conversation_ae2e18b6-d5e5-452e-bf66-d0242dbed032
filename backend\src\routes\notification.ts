import { Router } from 'express'
import { NotificationController } from '../controllers/notification'
import { authenticateToken } from '../middleware/auth'
import { validateRequest } from '../middleware/validation'
import { body, param, query } from 'express-validator'

const router = Router()

/**
 * 通知路由配置
 * 所有通知相关的API端点
 */

// 应用认证中间件到所有通知路由
router.use(authenticateToken)

/**
 * 获取通知列表
 * GET /api/notifications
 * 
 * Query参数:
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 20, 最大: 100)
 * - type: 通知类型过滤 (interaction|content|system|marketing)
 * - is_read: 已读状态过滤 (true|false)
 * - priority: 优先级过滤 (high|medium|low)
 */
router.get(
  '/',
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须是1-100之间的整数'),
    query('type')
      .optional()
      .isIn(['interaction', 'content', 'system', 'marketing'])
      .withMessage('通知类型必须是: interaction, content, system, marketing'),
    query('is_read')
      .optional()
      .isBoolean()
      .withMessage('已读状态必须是布尔值'),
    query('priority')
      .optional()
      .isIn(['high', 'medium', 'low'])
      .withMessage('优先级必须是: high, medium, low')
  ],
  validateRequest,
  NotificationController.getNotifications
)

/**
 * 获取未读通知数量
 * GET /api/notifications/unread-count
 */
router.get('/unread-count', NotificationController.getUnreadCount)

/**
 * 获取通知偏好设置
 * GET /api/notifications/preferences
 */
router.get('/preferences', NotificationController.getPreferences)

/**
 * 更新通知偏好设置
 * PUT /api/notifications/preferences
 * 
 * Body参数:
 * - preferences: 偏好设置数组
 *   - notificationType: 通知类型
 *   - channel: 通知渠道
 *   - isEnabled: 是否启用
 */
router.put(
  '/preferences',
  [
    body('preferences')
      .isArray({ min: 1 })
      .withMessage('偏好设置必须是非空数组'),
    body('preferences.*.notificationType')
      .isIn(['interaction', 'content', 'system', 'marketing'])
      .withMessage('通知类型必须是: interaction, content, system, marketing'),
    body('preferences.*.channel')
      .isIn(['in_app', 'email', 'push'])
      .withMessage('通知渠道必须是: in_app, email, push'),
    body('preferences.*.isEnabled')
      .isBoolean()
      .withMessage('启用状态必须是布尔值')
  ],
  validateRequest,
  NotificationController.updatePreferences
)

/**
 * 标记所有通知为已读
 * PUT /api/notifications/all/read
 */
router.put('/all/read', NotificationController.markAllAsRead)

/**
 * 批量标记通知为已读
 * PUT /api/notifications/batch/read
 * 
 * Body参数:
 * - notificationIds: 通知ID数组
 */
router.put(
  '/batch/read',
  [
    body('notificationIds')
      .isArray({ min: 1 })
      .withMessage('通知ID列表必须是非空数组'),
    body('notificationIds.*')
      .isInt({ min: 1 })
      .withMessage('通知ID必须是正整数')
  ],
  validateRequest,
  NotificationController.markBatchAsRead
)

/**
 * 批量删除通知
 * DELETE /api/notifications/batch
 * 
 * Body参数:
 * - notificationIds: 通知ID数组
 */
router.delete(
  '/batch',
  [
    body('notificationIds')
      .isArray({ min: 1 })
      .withMessage('通知ID列表必须是非空数组'),
    body('notificationIds.*')
      .isInt({ min: 1 })
      .withMessage('通知ID必须是正整数')
  ],
  validateRequest,
  NotificationController.deleteBatchNotifications
)

/**
 * 标记单个通知为已读
 * PUT /api/notifications/:id/read
 * 
 * Path参数:
 * - id: 通知ID
 */
router.put(
  '/:id/read',
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('通知ID必须是正整数')
  ],
  validateRequest,
  NotificationController.markAsRead
)

/**
 * 删除单个通知
 * DELETE /api/notifications/:id
 * 
 * Path参数:
 * - id: 通知ID
 */
router.delete(
  '/:id',
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('通知ID必须是正整数')
  ],
  validateRequest,
  NotificationController.deleteNotification
)

export default router
